<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ModifyNasfundYearJoinedToVarchar extends Migration
{
    public function up()
    {
        // Use raw SQL to modify the nasfund_year_joined column from YEAR to VARCHAR
        // This allows for more flexible year input (e.g., "2023", "N/A", "Unknown", etc.)
        $this->db->query('ALTER TABLE employee_personal_information MODIFY COLUMN nasfund_year_joined VARCHAR(10) NULL');
    }

    public function down()
    {
        // Revert the column back to YEAR type
        // Note: This may cause data loss if non-year values were stored
        $this->db->query('ALTER TABLE employee_personal_information MODIFY COLUMN nasfund_year_joined YEAR NULL');
    }
}
