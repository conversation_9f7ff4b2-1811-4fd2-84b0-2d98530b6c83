<?php

namespace App\Controllers\Agency;

use App\Models\AdminUserModel;

class AgencyProfileController extends AgencyBaseController
{
    protected $adminUserModel;

    public function __construct()
    {
        parent::__construct();
        $this->adminUserModel = new AdminUserModel();
    }

    /**
     * Display agency user profile
     */
    public function index()
    {
        $data = [
            'pageTitle' => 'My Profile',
            'breadcrumbs' => [
                ['title' => 'Dashboard', 'url' => '/agency/dashboard'],
                ['title' => 'Profile', 'url' => '']
            ]
        ];

        // Get current user details
        $data['user'] = $this->getCurrentUser();

        return view('agency/profile/profile_index', $data);
    }

    /**
     * Update agency user profile
     */
    public function update()
    {
        $validation = \Config\Services::validation();
        $validation->setRules([
            'first_name' => 'required|max_length[50]',
            'last_name' => 'required|max_length[50]',
            'email' => 'required|valid_email|max_length[100]',
            'phone' => 'permit_empty|max_length[20]',
            'current_password' => 'permit_empty|min_length[8]',
            'new_password' => 'permit_empty|min_length[8]',
            'confirm_password' => 'permit_empty|matches[new_password]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return redirect()->back()->withInput()->with('validation', $validation);
        }

        try {
            $updateData = [
                'first_name' => $this->request->getPost('first_name'),
                'last_name' => $this->request->getPost('last_name'),
                'email' => $this->request->getPost('email'),
                'phone' => $this->request->getPost('phone')
            ];

            // Check if password change is requested
            $currentPassword = $this->request->getPost('current_password');
            $newPassword = $this->request->getPost('new_password');

            $currentUser = $this->getCurrentUser();
            $userId = $currentUser['id'];

            if (!empty($currentPassword) && !empty($newPassword)) {
                // Verify current password
                $userRecord = $this->adminUserModel->find($userId);
                if (!password_verify($currentPassword, $userRecord['password_hash'])) {
                    return redirect()->back()->withInput()
                                   ->with('flash_error', 'Current password is incorrect.');
                }

                // Hash new password
                $updateData['password_hash'] = password_hash($newPassword, PASSWORD_DEFAULT);
            }

            // Check if email is already taken by another user
            $existingUser = $this->adminUserModel->where('email', $updateData['email'])
                                                 ->where('id !=', $userId)
                                                 ->first();
            if ($existingUser) {
                return redirect()->back()->withInput()
                               ->with('flash_error', 'Email address is already in use by another user.');
            }

            $this->adminUserModel->update($userId, $updateData);

            return redirect()->to('/agency/profile')
                           ->with('flash_success', 'Profile updated successfully.');

        } catch (\Exception $e) {
            log_message('error', 'Profile update failed: ' . $e->getMessage());
            return redirect()->back()->withInput()
                           ->with('flash_error', 'Failed to update profile. Please try again.');
        }
    }
}
