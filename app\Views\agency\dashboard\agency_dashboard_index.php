<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<!-- Mobile-First Dashboard Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="text-center mb-4">
            <h1 class="display-6 fw-bold text-primary mb-2">
                <i class="bi bi-speedometer2"></i> Agency Dashboard
            </h1>
            <p class="text-muted">Manage your employees and track progress</p>
        </div>
    </div>
</div>

<!-- Mobile-Optimized Statistics Cards - Employee Stats Removed -->
<div class="row mb-4">
    <!-- Agency Overview -->
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%);">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-building" style="font-size: 3rem;"></i>
                <h2 class="fw-bold mt-2 mb-1">1</h2>
                <p class="mb-0 opacity-75">Agency Portal</p>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%);">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-check-circle-fill" style="font-size: 3rem;"></i>
                <h2 class="fw-bold mt-2 mb-1">Online</h2>
                <p class="mb-0 opacity-75">System Status</p>
            </div>
        </div>
    </div>

    <!-- Active Features -->
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%);">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-gear-fill" style="font-size: 3rem;"></i>
                <h2 class="fw-bold mt-2 mb-1">6</h2>
                <p class="mb-0 opacity-75">Features</p>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="col-6 col-md-3 mb-3">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #C8102E 0%, #A60D26 100%);">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-bell-fill" style="font-size: 3rem;"></i>
                <h2 class="fw-bold mt-2 mb-1">0</h2>
                <p class="mb-0 opacity-75">Alerts</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Action Buttons - Mobile App Style -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="fw-bold mb-3 text-center">
            <i class="bi bi-lightning-charge text-warning"></i> Quick Actions
        </h4>
    </div>
</div>

<div class="row g-3 mb-4">
    <!-- Agency Settings -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/settings') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-gear-fill" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Settings</span>
        </a>
    </div>

    <!-- Employee OnBoarding -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-person-plus-fill" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">OnBoarding</span>
        </a>
    </div>

    <!-- Agency Profile -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/profile') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-building" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Agency Profile</span>
        </a>
    </div>

    <!-- Manage Documents -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/documents') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-file-earmark-text-fill" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Documents</span>
        </a>
    </div>

    <!-- Banking Info -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/banking') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-bank2" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Banking</span>
        </a>
    </div>

    <!-- Profile Links -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/profile-links') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-link-45deg" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Profile Links</span>
        </a>
    </div>

    <!-- Help & Support -->
    <div class="col-6 col-md-4">
        <a href="#" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #C8102E 0%, #A60D26 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-question-circle-fill" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Help & Support</span>
        </a>
    </div>

    <!-- Reports -->
    <div class="col-6 col-md-4">
        <a href="<?= base_url('agency/reports') ?>" class="btn btn-lg w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4 border-0 shadow-sm"
           style="background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%); color: white; min-height: 120px; border-radius: 1rem;">
            <i class="bi bi-graph-up" style="font-size: 2.5rem;"></i>
            <span class="fw-bold mt-2">Reports</span>
        </a>
    </div>
</div>

<!-- Agency Overview -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="fw-bold mb-3 text-center">
            <i class="bi bi-building text-info"></i> Agency Overview
        </h4>
    </div>
</div>

<div class="row g-3 mb-4">
    <!-- System Information -->
    <div class="col-md-6">
        <div class="card border-0 shadow-sm" style="border-radius: 1rem;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 60px; height: 60px; background: linear-gradient(135deg, #1A4E8C 0%, #164080 100%);">
                        <i class="bi bi-shield-check text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-1">System Status</h5>
                        <p class="text-muted mb-0">Portal health and security</p>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="fw-bold text-success">Online</h3>
                        <small class="text-muted">Status</small>
                    </div>
                    <div class="col-6">
                        <h3 class="fw-bold text-success">Secure</h3>
                        <small class="text-muted">Connection</small>
                    </div>
                </div>

                <div class="progress mt-3" style="height: 8px; border-radius: 10px;">
                    <div class="progress-bar bg-success" role="progressbar"
                         style="width: 100%; border-radius: 10px;">
                    </div>
                </div>
                <div class="text-center mt-2">
                    <small class="fw-bold text-success">100% Operational</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Agency Features -->
    <div class="col-md-6">
        <div class="card border-0 shadow-sm" style="border-radius: 1rem;">
            <div class="card-body p-4">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 60px; height: 60px; background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);">
                        <i class="bi bi-gear-fill text-white" style="font-size: 1.5rem;"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold mb-1">Available Features</h5>
                        <p class="text-muted mb-0">Portal capabilities</p>
                    </div>
                </div>

                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="fw-bold text-info">6</h3>
                        <small class="text-muted">Active</small>
                    </div>
                    <div class="col-6">
                        <h3 class="fw-bold text-accent">0</h3>
                        <small class="text-muted">Pending</small>
                    </div>
                </div>

                <div class="progress mt-3" style="height: 8px; border-radius: 10px;">
                    <div class="progress-bar bg-info" role="progressbar"
                         style="width: 100%; border-radius: 10px;">
                    </div>
                </div>
                <div class="text-center mt-2">
                    <small class="fw-bold text-info">All Features Available</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <!-- System Activity -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm" style="border-radius: 1rem;">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center p-4">
                <h5 class="fw-bold mb-0">
                    <i class="bi bi-activity text-primary"></i> System Activity
                </h5>
                <a href="<?= base_url('agency/reports') ?>" class="btn btn-sm btn-primary rounded-pill px-3">View Reports</a>
            </div>
            <div class="card-body p-4 pt-0">
                <div class="text-center py-5">
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 80px; height: 80px;">
                        <i class="bi bi-graph-up text-success" style="font-size: 2rem;"></i>
                    </div>
                    <h6 class="fw-bold text-success">System Running Smoothly</h6>
                    <p class="text-muted mb-3">All systems operational</p>
                    <small class="text-muted">Last updated: <?= date('M d, Y H:i') ?></small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="col-md-6 mb-4">
        <div class="card border-0 shadow-sm" style="border-radius: 1rem;">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center p-4">
                <h5 class="fw-bold mb-0">
                    <i class="bi bi-link-45deg text-info"></i> Quick Links
                </h5>
                <a href="<?= base_url('agency/profile') ?>" class="btn btn-sm btn-info rounded-pill px-3">Settings</a>
            </div>
            <div class="card-body p-4 pt-0">
                <div class="list-group list-group-flush">
                    <div class="list-group-item border-0 px-0 py-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-primary d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px;">
                                <i class="bi bi-building text-white"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="fw-bold mb-1">Agency Profile</h6>
                                <small class="text-muted">Manage agency information</small>
                            </div>
                            <div class="text-end">
                                <span class="badge rounded-pill bg-success">Active</span>
                            </div>
                        </div>
                    </div>

                    <div class="list-group-item border-0 px-0 py-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-info d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px;">
                                <i class="bi bi-gear text-white"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="fw-bold mb-1">System Settings</h6>
                                <small class="text-muted">Configure portal settings</small>
                            </div>
                            <div class="text-end">
                                <span class="badge rounded-pill bg-info">Available</span>
                            </div>
                        </div>
                    </div>

                    <div class="list-group-item border-0 px-0 py-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-success d-flex align-items-center justify-content-center me-3"
                                 style="width: 50px; height: 50px;">
                                <i class="bi bi-graph-up text-white"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="fw-bold mb-1">Reports & Analytics</h6>
                                <small class="text-muted">View system reports</small>
                            </div>
                            <div class="text-end">
                                <span class="badge rounded-pill bg-success">Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Agency Portal Features - Employee Management Removed -->

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
/* Mobile-First Dashboard Enhancements */
@media (max-width: 768px) {
    .display-6 {
        font-size: 1.75rem !important;
    }

    .btn-lg {
        font-size: 0.9rem;
        padding: 1rem 0.75rem;
    }

    .card-body {
        padding: 1rem !important;
    }
}

/* Touch-friendly hover effects */
.btn:hover {
    transform: translateY(-2px);
    transition: all 0.2s ease;
}

.card:hover {
    transform: translateY(-1px);
    transition: all 0.2s ease;
}

/* Improved button animations */
.btn {
    transition: all 0.2s ease;
}

.btn:active {
    transform: translateY(0);
}

/* Custom scrollbar for mobile */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Mobile-optimized dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add touch feedback for buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });

        button.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Auto-refresh dashboard stats every 5 minutes
    setInterval(function() {
        fetch('/agency/dashboard/stats')
            .then(response => response.json())
            .then(data => {
                console.log('Dashboard stats updated');
                // Update stats if needed
            })
            .catch(error => console.log('Stats update failed:', error));
    }, 300000); // 5 minutes

    // Add loading states for buttons
    const actionButtons = document.querySelectorAll('a[href^="/agency/"]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-hourglass-split';
            }
        });
    });
});


</script>
<?= $this->endSection() ?>
