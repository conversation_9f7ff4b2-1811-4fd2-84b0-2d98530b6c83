<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class EducationQualificationsSeeder extends Seeder
{
    public function run()
    {
        $data = [
            // Basic Qualifications
            [
                'qualification_name' => 'Primary School Certificate',
                'qualification_type' => 'basic',
                'description' => 'Elementary education completion certificate',
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Secondary School Certificate',
                'qualification_type' => 'basic',
                'description' => 'High school completion certificate',
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Grade 10 Certificate',
                'qualification_type' => 'basic',
                'description' => 'Grade 10 completion certificate',
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Grade 12 Certificate',
                'qualification_type' => 'basic',
                'description' => 'Grade 12 completion certificate',
                'sort_order' => 4,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Certificate IV',
                'qualification_type' => 'basic',
                'description' => 'Vocational certificate level 4',
                'sort_order' => 5,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Diploma',
                'qualification_type' => 'basic',
                'description' => 'Diploma level qualification',
                'sort_order' => 6,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Advanced Diploma',
                'qualification_type' => 'basic',
                'description' => 'Advanced diploma level qualification',
                'sort_order' => 7,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Bachelor Degree',
                'qualification_type' => 'basic',
                'description' => 'Undergraduate degree',
                'sort_order' => 8,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Honours Degree',
                'qualification_type' => 'basic',
                'description' => 'Honours level undergraduate degree',
                'sort_order' => 9,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Graduate Certificate',
                'qualification_type' => 'basic',
                'description' => 'Postgraduate certificate',
                'sort_order' => 10,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Graduate Diploma',
                'qualification_type' => 'basic',
                'description' => 'Postgraduate diploma',
                'sort_order' => 11,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Master Degree',
                'qualification_type' => 'basic',
                'description' => 'Masters level qualification',
                'sort_order' => 12,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Doctoral Degree (PhD)',
                'qualification_type' => 'basic',
                'description' => 'Doctorate level qualification',
                'sort_order' => 13,
                'created_at' => date('Y-m-d H:i:s'),
            ],

            // Additional Qualifications
            [
                'qualification_name' => 'Professional Certification',
                'qualification_type' => 'additional',
                'description' => 'Industry-specific professional certification',
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Short Course Certificate',
                'qualification_type' => 'additional',
                'description' => 'Short-term training course certificate',
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Workshop Certificate',
                'qualification_type' => 'additional',
                'description' => 'Workshop attendance certificate',
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Seminar Certificate',
                'qualification_type' => 'additional',
                'description' => 'Seminar attendance certificate',
                'sort_order' => 4,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Online Course Certificate',
                'qualification_type' => 'additional',
                'description' => 'Online learning course certificate',
                'sort_order' => 5,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Training Program Certificate',
                'qualification_type' => 'additional',
                'description' => 'Specialized training program certificate',
                'sort_order' => 6,
                'created_at' => date('Y-m-d H:i:s'),
            ],
            [
                'qualification_name' => 'Continuing Education Certificate',
                'qualification_type' => 'additional',
                'description' => 'Continuing professional development certificate',
                'sort_order' => 7,
                'created_at' => date('Y-m-d H:i:s'),
            ],
        ];

        $this->db->table('education_qualifications')->insertBatch($data);
    }
}
