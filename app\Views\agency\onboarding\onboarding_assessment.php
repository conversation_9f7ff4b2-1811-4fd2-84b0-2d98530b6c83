<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<style>
/* Mobile-friendly action buttons for assessment */
@media (max-width: 767.98px) {
    .action-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.25rem;
        min-width: 45px;
    }
    .action-btn i {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }
    .action-btn span {
        font-size: 0.65rem;
        line-height: 1;
    }
}

@media (min-width: 768px) {
    .action-btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
    }
    .action-btn i {
        font-size: 1rem;
    }
    .action-btn span {
        font-size: 0.75rem;
    }
}

@media print {
    .btn, .no-print { display: none !important; }
    .card { border: 1px solid #000 !important; margin-bottom: 1rem !important; }
}

.assessment-section { margin-bottom: 2rem; }
.section-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #0d6efd;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
}
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}
.info-item {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
}
.info-label { font-weight: 600; color: #495057; font-size: 0.875rem; margin-bottom: 0.25rem; }
.info-value { color: #212529; font-size: 1rem; }
.completion-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}
.completion-high { background-color: #d4edda; color: #155724; }
.completion-medium { background-color: #fff3cd; color: #856404; }
.completion-low { background-color: #f8d7da; color: #721c24; }
</style>

<!-- Navigation -->
<div class="row mb-4 no-print">
    <div class="col-6">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div class="col-6 text-end">
        <div class="btn-group">
            <button type="button" class="btn btn-success" onclick="window.print()">
                <i class="bi bi-printer"></i> Print Assessment
            </button>
            <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Edit Profile
            </a>
        </div>
    </div>
</div>

<!-- Assessment Header -->
<div class="text-center mb-4">
    <h1 class="mb-2">
        <i class="bi bi-clipboard-check text-primary"></i>
        Employee Assessment Report
    </h1>
    <h3 class="text-primary"><?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?></h3>
    <p class="text-muted">
        <?php if (!empty($employee['employment_number'])): ?>
            File Number: <?= esc($employee['employment_number']) ?> •
        <?php endif; ?>
        Generated on <?= date('F d, Y \a\t g:i A') ?>
    </p>
</div>

<!-- Completion Overview -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-speedometer2"></i> Profile Completion Overview</h4>
    </div>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-primary"><?= $education_stats['total_qualifications'] ?></h5>
                    <p class="mb-0">Education Records</p>
                    <span class="completion-indicator <?= $education_stats['total_qualifications'] > 0 ? 'completion-high' : 'completion-low' ?>">
                        <?= $education_stats['total_qualifications'] > 0 ? 'Complete' : 'Incomplete' ?>
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-success"><?= $membership_stats['total_memberships'] ?></h5>
                    <p class="mb-0">Professional Membership</p>
                    <span class="completion-indicator <?= $membership_stats['active_memberships'] > 0 ? 'completion-high' : 'completion-medium' ?>">
                        <?= $membership_stats['active_memberships'] ?> Active
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="text-warning"><?= $files_stats['total_files'] ?></h5>
                    <p class="mb-0">Personal Files</p>
                    <span class="completion-indicator <?= $files_stats['verified_files'] > 0 ? 'completion-high' : 'completion-medium' ?>">
                        <?= $files_stats['verified_files'] ?> Verified
                    </span>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <?php
                    $importantFields = ['first_name', 'last_name', 'gender', 'date_of_birth', 'nid_number', 'mobile_number', 'employment_number', 'designation', 'department', 'home_province', 'home_district', 'bank_name', 'account_number'];
                    $completedFields = 0;
                    foreach ($importantFields as $field) {
                        if (!empty($employee[$field])) { $completedFields++; }
                    }
                    $profileCompletion = round(($completedFields / count($importantFields)) * 100);
                    ?>
                    <h5 class="text-info"><?= $profileCompletion ?>%</h5>
                    <p class="mb-0">Profile Complete</p>
                    <span class="completion-indicator <?= $profileCompletion >= 80 ? 'completion-high' : ($profileCompletion >= 60 ? 'completion-medium' : 'completion-low') ?>">
                        <?= $profileCompletion >= 80 ? 'Excellent' : ($profileCompletion >= 60 ? 'Good' : 'Needs Work') ?>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Personal Information Section -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-person-circle"></i> Personal Information</h4>
    </div>
    
    <!-- Basic Information -->
    <h6 class="fw-bold mb-3 text-primary"><i class="bi bi-person"></i> Basic Information</h6>
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">Full Name</div>
            <div class="info-value"><?= esc($employee['first_name'] . ' ' . (!empty($employee['middle_name']) ? $employee['middle_name'] . ' ' : '') . $employee['last_name']) ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Gender</div>
            <div class="info-value"><?= !empty($employee['gender']) ? ucfirst($employee['gender']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Date of Birth</div>
            <div class="info-value"><?= !empty($employee['date_of_birth']) ? date('F d, Y', strtotime($employee['date_of_birth'])) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">National ID Number</div>
            <div class="info-value"><?= !empty($employee['nid_number']) ? esc($employee['nid_number']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Marital Status</div>
            <div class="info-value"><?= !empty($employee['marital_status']) ? ucfirst($employee['marital_status']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Spouse Name</div>
            <div class="info-value"><?= !empty($employee['spouse_name']) ? esc($employee['spouse_name']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Number of Children</div>
            <div class="info-value"><?= isset($employee['number_of_children']) ? $employee['number_of_children'] : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
    </div>
    
    <!-- Contact Information -->
    <h6 class="fw-bold mb-3 mt-4 text-primary"><i class="bi bi-telephone"></i> Contact Information</h6>
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">Email Address</div>
            <div class="info-value"><?= !empty($employee['email_address']) ? esc($employee['email_address']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Mobile Number</div>
            <div class="info-value"><?= !empty($employee['mobile_number']) ? esc($employee['mobile_number']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Emergency Contact Person</div>
            <div class="info-value"><?= !empty($employee['emergency_contact_person']) ? esc($employee['emergency_contact_person']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Emergency Contact Phone</div>
            <div class="info-value"><?= !empty($employee['emergency_contact_phone']) ? esc($employee['emergency_contact_phone']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
    </div>
    
    <!-- Address Information -->
    <h6 class="fw-bold mb-3 mt-4 text-primary"><i class="bi bi-geo-alt"></i> Address Information</h6>
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">Home Province</div>
            <div class="info-value"><?= !empty($employee['home_province']) ? esc($employee['home_province']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Home District</div>
            <div class="info-value"><?= !empty($employee['home_district']) ? esc($employee['home_district']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Home Village</div>
            <div class="info-value"><?= !empty($employee['home_village']) ? esc($employee['home_village']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
    </div>
    
    <!-- Banking Information -->
    <h6 class="fw-bold mb-3 mt-4 text-primary"><i class="bi bi-bank"></i> Banking Information</h6>
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">Bank Name</div>
            <div class="info-value"><?= !empty($employee['bank_name']) ? esc($employee['bank_name']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Bank Branch</div>
            <div class="info-value"><?= !empty($employee['bank_branch']) ? esc($employee['bank_branch']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Account Name</div>
            <div class="info-value"><?= !empty($employee['account_name']) ? esc($employee['account_name']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Account Number</div>
            <div class="info-value"><?= !empty($employee['account_number']) ? esc($employee['account_number']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Account Type</div>
            <div class="info-value"><?= !empty($employee['account_type']) ? esc($employee['account_type']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
    </div>
    
    <!-- NASFUND Information -->
    <h6 class="fw-bold mb-3 mt-4 text-primary"><i class="bi bi-shield-check"></i> NASFUND Information</h6>
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">NASFUND Member Name</div>
            <div class="info-value"><?= !empty($employee['nasfund_member_name']) ? esc($employee['nasfund_member_name']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">NASFUND Membership Number</div>
            <div class="info-value"><?= !empty($employee['nasfund_membership_number']) ? esc($employee['nasfund_membership_number']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">NASFUND Year Joined</div>
            <div class="info-value"><?= !empty($employee['nasfund_year_joined']) ? esc($employee['nasfund_year_joined']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">NASFUND Branch</div>
            <div class="info-value"><?= !empty($employee['nasfund_branch']) ? esc($employee['nasfund_branch']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
    </div>
    
    <!-- Employee Photo -->
    <?php if (!empty($employee['employee_id_photo'])): ?>
    <h6 class="fw-bold mb-3 mt-4 text-primary"><i class="bi bi-image"></i> Employee Photo</h6>
    <div class="text-center">
        <img src="<?= base_url($employee['employee_id_photo']) ?>" alt="Employee Photo" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
    </div>
    <?php endif; ?>
</div>

<!-- Employment Information -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-briefcase"></i> Employment Information</h4>
    </div>
    
    <div class="info-grid">
        <div class="info-item">
            <div class="info-label">Employment Number</div>
            <div class="info-value"><?= !empty($employee['employment_number']) ? esc($employee['employment_number']) : '<span class="text-muted">Not assigned</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Designation</div>
            <div class="info-value"><?= !empty($employee['designation']) ? esc($employee['designation']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Department</div>
            <div class="info-value"><?= !empty($employee['department']) ? esc($employee['department']) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
        <div class="info-item">
            <div class="info-label">Date of Commencement</div>
            <div class="info-value"><?= !empty($employee['date_of_commencement']) ? date('F d, Y', strtotime($employee['date_of_commencement'])) : '<span class="text-muted">Not provided</span>' ?></div>
        </div>
    </div>
</div>

<!-- Education Section -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-mortarboard"></i> Education & Qualifications (<?= count($education_records) ?> records)</h4>
    </div>
    
    <?php if (empty($education_records)): ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            No education records found. Employee should add their educational qualifications.
        </div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Qualification Type</th>
                        <th>Qualification Name</th>
                        <th>Institution</th>
                        <th>Completion Year</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($education_records as $education): ?>
                        <tr>
                            <td><?= esc($education['qualification_type']) ?></td>
                            <td><?= esc($education['qualification_name']) ?></td>
                            <td><?= esc($education['institution']) ?></td>
                            <td><?= esc($education['completion_year']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Professional Memberships Section -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-award"></i> Professional Memberships & Licenses (<?= count($membership_records) ?> records)</h4>
    </div>
    
    <?php if (empty($membership_records)): ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            No professional membership records found.
        </div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Professional Affiliation</th>
                        <th>License Number</th>
                        <th>Status</th>
                        <th>Renewal Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($membership_records as $membership): ?>
                        <tr>
                            <td><?= esc($membership['professional_affiliation']) ?></td>
                            <td><?= !empty($membership['license_number']) ? esc($membership['license_number']) : '-' ?></td>
                            <td><?= ucfirst($membership['current_status']) ?></td>
                            <td><?= !empty($membership['renewal_date']) ? date('M d, Y', strtotime($membership['renewal_date'])) : '-' ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Employment History Section -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-clock-history"></i> Employment History (<?= count($employment_history) ?> records)</h4>
    </div>
    
    <?php if (empty($employment_history)): ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            No employment history records found.
        </div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Employer</th>
                        <th>Designation</th>
                        <th>Start Date</th>
                        <th>End Date</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($employment_history as $employment): ?>
                        <tr>
                            <td><?= esc($employment['employer_name']) ?></td>
                            <td><?= esc($employment['designation']) ?></td>
                            <td><?= !empty($employment['start_date']) ? date('M Y', strtotime($employment['start_date'])) : '-' ?></td>
                            <td>
                                <?php if ($employment['is_current']): ?>
                                    Current
                                <?php elseif (!empty($employment['end_date'])): ?>
                                    <?= date('M Y', strtotime($employment['end_date'])) ?>
                                <?php else: ?>
                                    -
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($employment['is_current']): ?>
                                    Current Position
                                <?php else: ?>
                                    Previous
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Files Section -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-file-earmark"></i> Personal Files (<?= count($file_records) ?> files)</h4>
    </div>
    
    <?php if (empty($file_records)): ?>
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            No personal files uploaded.
        </div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>File Title</th>
                        <th>Description</th>
                        <th>Upload Date</th>
                        <th>Verification Status</th>
                        <th class="no-print" width="120px">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($file_records as $file): ?>
                        <?php
                        $extension = pathinfo($file['file_path'], PATHINFO_EXTENSION);
                        $iconClass = match(strtolower($extension)) {
                            'pdf' => 'bi-file-earmark-pdf text-danger',
                            'doc', 'docx' => 'bi-file-earmark-word text-primary',
                            'jpg', 'jpeg', 'png' => 'bi-file-earmark-image text-success',
                            default => 'bi-file-earmark text-secondary'
                        };
                        ?>
                        <tr id="file-row-<?= $file['id'] ?>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="<?= $iconClass ?> me-2" style="font-size: 1.2rem;"></i>
                                    <div>
                                        <strong><?= esc($file['file_title']) ?></strong>
                                        <?php if (!empty($file['file_path'])): ?>
                                            <br><small class="text-muted">
                                                <i class="bi bi-paperclip"></i> 
                                                <?= basename($file['file_path']) ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td><?= !empty($file['file_description']) ? esc($file['file_description']) : '<span class="text-muted">No description</span>' ?></td>
                            <td>
                                <small>
                                    <?= date('M d, Y', strtotime($file['created_at'])) ?>
                                    <br><span class="text-muted"><?= date('g:i A', strtotime($file['created_at'])) ?></span>
                                </small>
                            </td>
                            <td>
                                <span class="verification-status-<?= $file['id'] ?>">
                                    <?php if ($file['is_verified']): ?>
                                        Verified
                                    <?php else: ?>
                                        Pending Verification
                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="no-print">
                                <div class="d-flex gap-1 justify-content-center flex-wrap">
                                    <button type="button" class="btn btn-outline-info action-btn" 
                                            onclick="viewFile('<?= base_url($file['file_path']) ?>', '<?= esc($file['file_title']) ?>', '<?= strtolower($extension) ?>')" 
                                            title="View File">
                                        <i class="bi bi-eye"></i>
                                        <span class="d-none d-sm-inline">View</span>
                                    </button>
                                    <button type="button" class="btn btn-outline-<?= $file['is_verified'] ? 'warning' : 'success' ?> action-btn"
                                            onclick="toggleFileVerification(<?= $file['id'] ?>, <?= $file['is_verified'] ? 'false' : 'true' ?>)" 
                                            title="<?= $file['is_verified'] ? 'Mark as Unverified' : 'Mark as Verified' ?>">
                                        <i class="bi bi-<?= $file['is_verified'] ? 'x-circle' : 'check-circle' ?>"></i>
                                        <span class="d-none d-sm-inline"><?= $file['is_verified'] ? 'Unverify' : 'Verify' ?></span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>

<!-- Assessment Summary -->
<div class="assessment-section">
    <div class="section-header">
        <h4 class="mb-0"><i class="bi bi-clipboard-check"></i> Assessment Summary</h4>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <h6 class="fw-bold">Completeness Analysis</h6>
            <ul class="list-unstyled">
                <li class="mb-2">
                    <i class="bi bi-person-check text-<?= $profileCompletion >= 80 ? 'success' : ($profileCompletion >= 60 ? 'warning' : 'danger') ?>"></i>
                    <strong>Personal Information:</strong> <?= $profileCompletion ?>% complete
                </li>
                <li class="mb-2">
                    <i class="bi bi-mortarboard text-<?= $education_stats['total_qualifications'] > 0 ? 'success' : 'danger' ?>"></i>
                    <strong>Education:</strong> <?= $education_stats['total_qualifications'] ?> qualification(s) recorded
                </li>
                <li class="mb-2">
                    <i class="bi bi-award text-<?= $membership_stats['total_memberships'] > 0 ? 'success' : 'warning' ?>"></i>
                    <strong>Professional Membership:</strong> <?= $membership_stats['total_memberships'] ?> membership(s), <?= $membership_stats['active_memberships'] ?> active
                </li>
                <li class="mb-2">
                    <i class="bi bi-file-earmark text-<?= $files_stats['total_files'] > 0 ? 'success' : 'warning' ?>"></i>
                    <strong>Personal Files:</strong> <?= $files_stats['total_files'] ?> file(s), <?= $files_stats['verified_files'] ?> verified
                </li>
                <li class="mb-2">
                    <i class="bi bi-briefcase text-<?= $employment_stats['total_employments'] > 0 ? 'success' : 'warning' ?>"></i>
                    <strong>Employment History:</strong> <?= $employment_stats['total_employments'] ?> record(s)
                </li>
            </ul>
        </div>
        <div class="col-md-4">
            <h6 class="fw-bold">Recommended Actions</h6>
            <ul class="list-unstyled">
                <?php if ($profileCompletion < 80): ?>
                    <li class="mb-1"><i class="bi bi-exclamation-triangle text-warning"></i> Complete personal information</li>
                <?php endif; ?>
                <?php if ($education_stats['total_qualifications'] == 0): ?>
                    <li class="mb-1"><i class="bi bi-exclamation-triangle text-danger"></i> Add education records</li>
                <?php endif; ?>
                <?php if ($membership_stats['expired_memberships'] > 0): ?>
                    <li class="mb-1"><i class="bi bi-exclamation-triangle text-warning"></i> Update expired licenses</li>
                <?php endif; ?>
                <?php if ($profileCompletion >= 80 && $education_stats['total_qualifications'] > 0): ?>
                    <li class="mb-1"><i class="bi bi-check-circle text-success"></i> Profile looks complete</li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</div>

<div class="text-center mt-5 pt-4 border-top">
    <p class="text-muted">
        <small>Assessment generated by Agency Portal • <?= date('F d, Y \a\t g:i A') ?></small>
    </p>
</div>

<!-- File Viewer Modal -->
<div class="modal fade" id="assessmentFileViewer" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assessmentFileTitle">PDF Viewer</h5>
                <div class="d-flex align-items-center gap-3">
                    <!-- PDF Controls -->
                    <div id="assessmentPdfControls" class="d-none">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="prevPage" title="Previous Pages">
                                <i class="bi bi-chevron-left"></i>
                            </button>
                            <span class="btn btn-outline-secondary disabled" id="pageInfo">Pages 1-5 of 0</span>
                            <button type="button" class="btn btn-outline-secondary" id="nextPage" title="Next Pages">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                        <div class="btn-group ms-2" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="zoomOut" title="Zoom Out">
                                <i class="bi bi-zoom-out"></i>
                            </button>
                            <span class="btn btn-outline-secondary disabled" id="zoomLevel">100%</span>
                            <button type="button" class="btn btn-outline-secondary" id="zoomIn" title="Zoom In">
                                <i class="bi bi-zoom-in"></i>
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>
            <div class="modal-body p-0">
                <div id="assessmentFileContent" class="h-100 d-flex align-items-center justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<script>
// PDF.js worker configuration
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// Employee and file data
const employeeId = <?= $employee['id'] ?>;
const baseUrl = '<?= base_url() ?>';

// PDF viewer variables
let currentPdf = null;
let currentPageGroup = 1;
let totalPages = 0;
let currentScale = 1.0;
const minScale = 0.5;
const maxScale = 3.0;
const scaleStep = 0.25;
const pagesPerGroup = 5;

// Load and render PDF
function loadPDF(pdfUrl) {
    const content = document.getElementById('assessmentFileContent');
    const controls = document.getElementById('assessmentPdfControls');
    
    // Show loading spinner
    content.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    `;
    
    pdfjsLib.getDocument(pdfUrl).promise.then(function(pdf) {
        currentPdf = pdf;
        totalPages = pdf.numPages;
        currentPageGroup = 1;
        currentScale = 1.0;
        
        // Show controls
        if (controls) {
            controls.classList.remove('d-none');
        }
        
        updatePageInfo();
        renderPageGroup();
        updateNavigationButtons();
    }).catch(function(error) {
        console.error('Error loading PDF:', error);
        content.innerHTML = `
            <div class="text-center p-5">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                <h5 class="text-danger mt-3">Error Loading PDF</h5>
                <p class="text-muted">Unable to load the PDF file. Please try downloading it directly.</p>
                <a href="${pdfUrl}" target="_blank" class="btn btn-primary">
                    <i class="bi bi-download"></i> Download PDF
                </a>
            </div>
        `;
    });
}

// Render current group of PDF pages
function renderPageGroup() {
    if (!currentPdf) return;
    
    const startPage = (currentPageGroup - 1) * pagesPerGroup + 1;
    const endPage = Math.min(startPage + pagesPerGroup - 1, totalPages);
    
    const content = document.getElementById('assessmentFileContent');
    content.innerHTML = '<div class="pdf-container p-3" style="overflow-y: auto; height: 100%; text-align: center;"></div>';
    const container = content.querySelector('.pdf-container');
    
    const promises = [];
    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        promises.push(renderPage(pageNum, container));
    }
    
    Promise.all(promises).then(() => {
        console.log(`Rendered pages ${startPage}-${endPage}`);
    });
}

// Render individual PDF page
function renderPage(pageNum, container) {
    return currentPdf.getPage(pageNum).then(function(page) {
        const viewport = page.getViewport({ scale: currentScale });
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        canvas.style.marginBottom = '20px';
        canvas.style.display = 'block';
        canvas.style.marginLeft = 'auto';
        canvas.style.marginRight = 'auto';
        canvas.style.border = '1px solid #ddd';
        canvas.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';
        
        // Add page number label
        const pageLabel = document.createElement('div');
        pageLabel.textContent = `Page ${pageNum}`;
        pageLabel.style.textAlign = 'center';
        pageLabel.style.marginBottom = '10px';
        pageLabel.style.fontWeight = 'bold';
        pageLabel.style.color = '#666';
        
        container.appendChild(pageLabel);
        container.appendChild(canvas);
        
        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };
        
        return page.render(renderContext).promise;
    }).catch(function(error) {
        console.error('Error rendering page:', error);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-center p-3';
        errorDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
            <p class="text-danger mt-3">Error rendering page ${pageNum}</p>
        `;
        container.appendChild(errorDiv);
    });
}

// Update page info display
function updatePageInfo() {
    const startPage = (currentPageGroup - 1) * pagesPerGroup + 1;
    const endPage = Math.min(startPage + pagesPerGroup - 1, totalPages);
    const pageInfoElement = document.getElementById('pageInfo');
    const zoomLevelElement = document.getElementById('zoomLevel');
    
    if (pageInfoElement) {
        if (totalPages <= pagesPerGroup) {
            pageInfoElement.textContent = `Pages 1-${totalPages} of ${totalPages}`;
        } else {
            pageInfoElement.textContent = `Pages ${startPage}-${endPage} of ${totalPages}`;
        }
    }
    if (zoomLevelElement) {
        zoomLevelElement.textContent = `${Math.round(currentScale * 100)}%`;
    }
}

// Update navigation button states
function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    if (prevBtn) prevBtn.disabled = currentPageGroup <= 1;
    if (nextBtn) nextBtn.disabled = (currentPageGroup * pagesPerGroup) >= totalPages;
}

// View PDF file function
function viewPDF(filePath, fileName) {
    document.getElementById('assessmentFileTitle').textContent = fileName || 'PDF Viewer';
    loadPDF(filePath);
    new bootstrap.Modal(document.getElementById('assessmentFileViewer')).show();
}

// Comprehensive file viewer function
function viewFile(filePath, fileName, fileType) {
    const content = document.getElementById('assessmentFileContent');
    const controls = document.getElementById('assessmentPdfControls');
    
    document.getElementById('assessmentFileTitle').textContent = fileName || 'File Viewer';
    
    if (fileType === 'pdf') {
        viewPDF(filePath, fileName);
        return;
    }
    
    // Hide PDF controls for non-PDF files
    if (controls) controls.classList.add('d-none');
    
    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileType)) {
        // Show loading spinner
        content.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        `;
        
        const img = new Image();
        img.onload = function() {
            content.innerHTML = `
                <div class="text-center p-3 h-100 d-flex align-items-center justify-content-center">
                    <img src="${filePath}" alt="${fileName}" class="img-fluid" style="max-height: 90vh; max-width: 100%; object-fit: contain;">
                </div>
            `;
        };
        img.onerror = function() {
            content.innerHTML = `
                <div class="text-center p-5">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    <h5 class="text-danger mt-3">Error Loading Image</h5>
                    <p class="text-muted">Unable to load the image file. Please try downloading it directly.</p>
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="bi bi-download"></i> Download Image
                    </a>
                </div>
            `;
        };
        img.src = filePath;
    } else {
        content.innerHTML = `
            <div class="text-center p-5">
                <i class="bi bi-file-earmark text-muted" style="font-size: 4rem;"></i>
                <h5 class="text-muted mt-3">File Preview Not Available</h5>
                <p class="text-muted">This file type cannot be previewed in the browser.</p>
                <a href="${filePath}" target="_blank" class="btn btn-primary">
                    <i class="bi bi-download"></i> Download File
                </a>
            </div>
        `;
    }
    
    new bootstrap.Modal(document.getElementById('assessmentFileViewer')).show();
}

// Setup PDF navigation event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Previous page group
    const prevBtn = document.getElementById('prevPage');
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            if (currentPageGroup > 1) {
                currentPageGroup--;
                renderPageGroup();
                updatePageInfo();
                updateNavigationButtons();
            }
        });
    }
    
    // Next page group
    const nextBtn = document.getElementById('nextPage');
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if ((currentPageGroup * pagesPerGroup) < totalPages) {
                currentPageGroup++;
                renderPageGroup();
                updatePageInfo();
                updateNavigationButtons();
            }
        });
    }
    
    // Zoom in
    const zoomInBtn = document.getElementById('zoomIn');
    if (zoomInBtn) {
        zoomInBtn.addEventListener('click', function() {
            currentScale = Math.min(currentScale + scaleStep, maxScale);
            if (currentPdf) {
                renderPageGroup();
                updatePageInfo();
            }
        });
    }
    
    // Zoom out
    const zoomOutBtn = document.getElementById('zoomOut');
    if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', function() {
            currentScale = Math.max(currentScale - scaleStep, minScale);
            if (currentPdf) {
                renderPageGroup();
                updatePageInfo();
            }
        });
    }
    
    // Reset modal when closed
    const modal = document.getElementById('assessmentFileViewer');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            currentPdf = null;
            currentPageGroup = 1;
            totalPages = 0;
            currentScale = 1.0;
            
            const content = document.getElementById('assessmentFileContent');
            if (content) {
                content.innerHTML = `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                `;
            }
            
            const controls = document.getElementById('assessmentPdfControls');
            if (controls) {
                controls.classList.add('d-none');
            }
        });
    }
});

// File verification toggle function
function toggleFileVerification(fileId, isVerified) {
    const btn = event.target.closest('button');
    const originalIcon = btn.querySelector('i').className;
    const originalText = btn.querySelector('span')?.textContent;
    
    // Show loading state
    btn.disabled = true;
    btn.querySelector('i').className = 'bi bi-hourglass-split';
    if (btn.querySelector('span')) {
        btn.querySelector('span').textContent = 'Processing...';
    }
    
    fetch(`${baseUrl}agency/onboarding/${employeeId}/files/${fileId}/verify`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            is_verified: isVerified
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update button state
            btn.classList.remove('btn-outline-success', 'btn-outline-warning');
            if (data.is_verified) {
                btn.classList.add('btn-outline-warning');
                btn.querySelector('i').className = 'bi bi-x-circle';
                if (btn.querySelector('span')) {
                    btn.querySelector('span').textContent = 'Unverify';
                }
                btn.title = 'Mark as Unverified';
                btn.onclick = () => toggleFileVerification(fileId, false);
            } else {
                btn.classList.add('btn-outline-success');
                btn.querySelector('i').className = 'bi bi-check-circle';
                if (btn.querySelector('span')) {
                    btn.querySelector('span').textContent = 'Verify';
                }
                btn.title = 'Mark as Verified';
                btn.onclick = () => toggleFileVerification(fileId, true);
            }
            
            // Update status badge
            const statusElement = document.querySelector(`.verification-status-${fileId}`);
            if (statusElement) {
                if (data.is_verified) {
                    statusElement.innerHTML = `
                        <span class="badge bg-success">
                            <i class="bi bi-check-circle"></i> Verified
                        </span>
                    `;
                } else {
                    statusElement.innerHTML = `
                        <span class="badge bg-warning">
                            <i class="bi bi-clock"></i> Pending Verification
                        </span>
                    `;
                }
            }
            
            // Show success message
            showNotification(data.message, 'success');
        } else {
            // Restore original button state
            btn.querySelector('i').className = originalIcon;
            if (btn.querySelector('span')) {
                btn.querySelector('span').textContent = originalText;
            }
            
            // Show error message
            showNotification(data.message || 'Failed to update verification status', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        
        // Restore original button state
        btn.querySelector('i').className = originalIcon;
        if (btn.querySelector('span')) {
            btn.querySelector('span').textContent = originalText;
        }
        
        showNotification('An error occurred while updating verification status', 'error');
    })
    .finally(() => {
        btn.disabled = false;
    });
}

// Simple notification function
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' : type === 'error' ? 'alert-danger' : 'alert-info';
    const iconClass = type === 'success' ? 'bi-check-circle' : type === 'error' ? 'bi-exclamation-triangle' : 'bi-info-circle';
    
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="bi ${iconClass} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
<?= $this->endSection() ?>