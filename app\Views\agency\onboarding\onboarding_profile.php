<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<style>
/* Mobile-friendly navigation buttons */
@media (max-width: 767.98px) {
    .nav-btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.25rem;
    }
    .nav-btn i {
        font-size: 1.1rem;
        margin-bottom: 0.25rem;
    }
    .nav-btn .badge {
        font-size: 0.7rem;
        margin-left: 0 !important;
        margin-top: 0.25rem;
    }
}
</style>

<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-6">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div class="col-6 text-end">
        <!-- Additional actions can be added here -->
    </div>
</div>

<!-- Onboarding Steps Navigation -->
<div class="row mb-4">
    <div class="col-md-3 mb-2">
        <button class="btn btn-primary w-100" disabled>
            <i class="bi bi-person"></i><br>
            <small>Personal Info</small>
        </button>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/education') ?>" class="btn btn-outline-info w-100">
            <i class="bi bi-mortarboard"></i><br>
            <small>Education</small>
            <span class="badge bg-secondary ms-1"><?= $education_stats['total_qualifications'] ?></span>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/memberships') ?>" class="btn btn-outline-success w-100">
            <i class="bi bi-award"></i><br>
            <small>Memberships</small>
            <span class="badge bg-secondary ms-1"><?= $membership_stats['total_memberships'] ?></span>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/files') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-file-earmark"></i><br>
            <small>Files</small>
            <span class="badge bg-secondary ms-1"><?= $files_stats['total_files'] ?></span>
        </a>
    </div>
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-briefcase"></i><br>
            <small>Employment</small>
            <span class="badge bg-secondary ms-1"><?= $employment_stats['total_employments'] ?></span>
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-circle text-primary"></i>
        <?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?>
    </h2>
    <p class="text-muted mb-0">
        <?php if (!empty($employee['employment_number'])): ?>
            File Number: <?= esc($employee['employment_number']) ?> •
        <?php endif; ?>
        <?= esc($employee['email_address']) ?>
    </p>
</div>



<!-- Profile Stats Row -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <?php
                $completionFields = [
                    'nid_number', 'date_of_birth', 'mobile_number', 'designation',
                    'bank_name', 'account_number', 'nasfund_membership_number'
                ];
                $completedFields = 0;
                foreach ($completionFields as $field) {
                    if (!empty($employee[$field])) $completedFields++;
                }
                $completionPercentage = round(($completedFields / count($completionFields)) * 100);
                ?>
                <h4 class="text-primary"><?= $completionPercentage ?>%</h4>
                <p class="text-muted mb-0">Profile Complete</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info"><?= $education_stats['total_qualifications'] ?></h4>
                <p class="text-muted mb-0">Education Records</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success"><?= $membership_stats['total_memberships'] ?></h4>
                <p class="text-muted mb-0">Memberships</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning"><?= $employment_stats['total_employments'] ?></h4>
                <p class="text-muted mb-0">Employment History</p>
            </div>
        </div>
    </div>
</div>

<!-- Public Profile Access Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-link text-success"></i> Public Profile Access
                </h6>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= base_url('agency/onboarding/' . $employee['id'] . '/update-public-access') ?>">
                    <?= csrf_field() ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="publicAccess" name="public_profile_enabled" value="1"
                                       <?= $employee['public_profile_enabled'] ? 'checked' : '' ?>>
                                <label class="form-check-label" for="publicAccess">
                                    Enable Public Access
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="public_profile_expiry_date" class="form-label">Expiration Date</label>
                            <input type="date" class="form-control" id="public_profile_expiry_date" name="public_profile_expiry_date" 
                                   value="<?= esc($employee['public_profile_expiry_date'] ?? '') ?>">
                        </div>
                        <div class="col-md-3">
                            <?php if (!empty($employee['public_profile_token'])): ?>
                                <label class="form-label">Current Status</label>
                                <p class="text-success mb-0"><i class="bi bi-check-circle"></i> Link Active</p>
                                <?php if (!empty($employee['public_profile_expiry_date'])): ?>
                                    <small class="text-muted">Expires: <?= date('M d, Y', strtotime($employee['public_profile_expiry_date'])) ?></small>
                                <?php else: ?>
                                    <small class="text-muted">No expiry date set</small>
                                <?php endif; ?>
                            <?php else: ?>
                                <label class="form-label">Status</label>
                                <p class="text-muted mb-0">No active link</p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-success">
                                <i class="bi bi-link"></i> Update Access
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Main Profile Content - Full Width -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Employee Profile</h5>
        <div>
            <button type="submit" form="profileForm" class="btn btn-primary">
                <i class="bi bi-check-circle"></i> Save Profile
            </button>
        </div>
    </div>
    <div class="card-body">
        <form id="profileForm" method="POST" action="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile/update') ?>" enctype="multipart/form-data">
            <?= csrf_field() ?>
                    
                    <!-- Personal Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-accent border-bottom pb-2 mb-3">
                                <i class="bi bi-person-fill"></i> Personal Information
                            </h6>
                        </div>
                    </div>

                    <!-- Name and Email Fields -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name"
                                       value="<?= esc($employee['first_name'] ?? '') ?>" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="middle_name" class="form-label">Middle Name</label>
                                <input type="text" class="form-control" id="middle_name" name="middle_name"
                                       value="<?= esc($employee['middle_name'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name"
                                       value="<?= esc($employee['last_name'] ?? '') ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email_address" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="email_address" name="email_address"
                                       value="<?= esc($employee['email_address'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nid_number" class="form-label">National ID Number</label>
                                <input type="text" class="form-control" id="nid_number" name="nid_number"
                                       value="<?= esc($employee['nid_number'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="gender" class="form-label">Gender <span class="text-danger">*</span></label>
                                <select class="form-control" id="gender" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="male" <?= (isset($employee['gender']) && $employee['gender'] === 'male') ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= (isset($employee['gender']) && $employee['gender'] === 'female') ? 'selected' : '' ?>>Female</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth"
                                       value="<?= esc($employee['date_of_birth'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Empty column for spacing -->
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="marital_status" class="form-label">Marital Status</label>
                                <select class="form-select" id="marital_status" name="marital_status">
                                    <option value="">Select Status</option>
                                    <option value="single" <?= ($employee['marital_status'] ?? '') === 'single' ? 'selected' : '' ?>>Single</option>
                                    <option value="married" <?= ($employee['marital_status'] ?? '') === 'married' ? 'selected' : '' ?>>Married</option>
                                    <option value="divorced" <?= ($employee['marital_status'] ?? '') === 'divorced' ? 'selected' : '' ?>>Divorced</option>
                                    <option value="widowed" <?= ($employee['marital_status'] ?? '') === 'widowed' ? 'selected' : '' ?>>Widowed</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spouse_name" class="form-label">Spouse Name</label>
                                <input type="text" class="form-control" id="spouse_name" name="spouse_name" 
                                       value="<?= esc($employee['spouse_name'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="number_of_children" class="form-label">Number of Children</label>
                                <input type="number" class="form-control" id="number_of_children" name="number_of_children" 
                                       value="<?= esc($employee['number_of_children'] ?? '0') ?>" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="mobile_number" class="form-label">Mobile Number</label>
                                <input type="tel" class="form-control" id="mobile_number" name="mobile_number" 
                                       value="<?= esc($employee['mobile_number'] ?? '') ?>">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Employee ID Photo Upload -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="employee_id_photo" class="form-label">Official ID Document Photo</label>
                                <input type="file" class="form-control" id="employee_id_photo" name="employee_id_photo" 
                                       accept="image/*">
                                <div class="form-text">Upload a clear photo of the employee's official identification document (JPG, PNG, max 5MB)</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <?php if (!empty($employee['employee_id_photo'])): ?>
                                <div class="mb-3">
                                    <label class="form-label">Current Photo</label>
                                    <div class="border rounded p-2">
                                        <img src="<?= base_url($employee['employee_id_photo']) ?>" alt="Official ID Document Photo"
                                             class="img-fluid" style="max-height: 100px;">
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-accent border-bottom pb-2 mb-3 mt-4">
                                <i class="bi bi-geo-alt-fill"></i> Location Information
                            </h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="home_province" class="form-label">Home Province</label>
                                <input type="text" class="form-control" id="home_province" name="home_province" 
                                       value="<?= esc($employee['home_province'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="home_district" class="form-label">Home District</label>
                                <input type="text" class="form-control" id="home_district" name="home_district" 
                                       value="<?= esc($employee['home_district'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="home_village" class="form-label">Home Village</label>
                                <input type="text" class="form-control" id="home_village" name="home_village" 
                                       value="<?= esc($employee['home_village'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Emergency Contact -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-accent border-bottom pb-2 mb-3 mt-4">
                                <i class="bi bi-telephone-fill"></i> Emergency Contact
                            </h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emergency_contact_person" class="form-label">Contact Person</label>
                                <input type="text" class="form-control" id="emergency_contact_person" name="emergency_contact_person" 
                                       value="<?= esc($employee['emergency_contact_person'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emergency_contact_phone" class="form-label">Contact Phone</label>
                                <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone" 
                                       value="<?= esc($employee['emergency_contact_phone'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Employment Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-accent border-bottom pb-2 mb-3 mt-4">
                                <i class="bi bi-briefcase-fill"></i> Employment Information
                            </h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employment_number" class="form-label">
                                    Official Employee File Number
                                    <?php if (empty($employee['employment_number'])): ?>
                                        <span class="badge bg-warning text-dark ms-2">Not Assigned</span>
                                    <?php endif; ?>
                                </label>
                                <input type="text" class="form-control" id="employment_number" name="employment_number"
                                       value="<?= esc($employee['employment_number'] ?? '') ?>"
                                       placeholder="Enter employee file number">
                                <div class="form-text">
                                    This will be the official employee identification number used in all employee records.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="designation" class="form-label">Designation</label>
                                <input type="text" class="form-control" id="designation" name="designation"
                                       value="<?= esc($employee['designation'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department"
                                       value="<?= esc($employee['department'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_of_commencement" class="form-label">Date of Commencement</label>
                                <input type="date" class="form-control" id="date_of_commencement" name="date_of_commencement"
                                       value="<?= esc($employee['date_of_commencement'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Banking Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-accent border-bottom pb-2 mb-3 mt-4">
                                <i class="bi bi-bank"></i> Banking Information
                            </h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_name" class="form-label">Bank Name</label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name"
                                       value="<?= esc($employee['bank_name'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="bank_branch" class="form-label">Bank Branch</label>
                                <input type="text" class="form-control" id="bank_branch" name="bank_branch"
                                       value="<?= esc($employee['bank_branch'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_name" class="form-label">Account Name</label>
                                <input type="text" class="form-control" id="account_name" name="account_name"
                                       value="<?= esc($employee['account_name'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_number" class="form-label">Account Number</label>
                                <input type="text" class="form-control" id="account_number" name="account_number"
                                       value="<?= esc($employee['account_number'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="account_type" class="form-label">Account Type</label>
                                <input type="text" class="form-control" id="account_type" name="account_type"
                                       value="<?= esc($employee['account_type'] ?? '') ?>"
                                       placeholder="e.g., Savings, Current, Business">
                            </div>
                        </div>
                    </div>

                    <!-- NASFUND Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-accent border-bottom pb-2 mb-3 mt-4">
                                <i class="bi bi-shield-check"></i> NASFUND Information
                            </h6>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nasfund_member_name" class="form-label">Member Name</label>
                                <input type="text" class="form-control" id="nasfund_member_name" name="nasfund_member_name"
                                       value="<?= esc($employee['nasfund_member_name'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nasfund_membership_number" class="form-label">Membership Number</label>
                                <input type="text" class="form-control" id="nasfund_membership_number" name="nasfund_membership_number"
                                       value="<?= esc($employee['nasfund_membership_number'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nasfund_year_joined" class="form-label">Year Joined</label>
                                <input type="text" class="form-control" id="nasfund_year_joined" name="nasfund_year_joined"
                                       value="<?= esc($employee['nasfund_year_joined'] ?? '') ?>" placeholder="e.g., 2023, N/A, Unknown">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nasfund_branch" class="form-label">NASFUND Branch</label>
                                <input type="text" class="form-control" id="nasfund_branch" name="nasfund_branch"
                                       value="<?= esc($employee['nasfund_branch'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="text-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Save Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
