<?php

namespace App\Models;

use CodeIgniter\Model;

class EmployeePersonalInformationModel extends Model
{
    protected $table = 'employee_personal_information';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'employment_number',
        'agency_id',
        'nid_number',
        'first_name',
        'middle_name',
        'last_name',
        'email_address',
        'gender',
        'date_of_birth',
        'date_of_commencement',
        'marital_status',
        'spouse_name',
        'number_of_children',
        'home_province',
        'home_district',
        'home_village',
        'mobile_number',
        'emergency_contact_person',
        'emergency_contact_phone',
        'designation',
        'department',
        'bank_name',
        'bank_branch',
        'account_name',
        'account_number',
        'account_type',
        'nasfund_member_name',
        'nasfund_membership_number',
        'nasfund_year_joined',
        'nasfund_branch',
        'onboard_status',
        'onboard_status_at',
        'onboard_status_by',
        'onboard_status_remarks',
        'hr_review_status',
        'hr_review_status_at',
        'hr_review_status_by',
        'supervisor_review_status',
        'supervisor_review_status_at',
        'supervisor_review_status_by',
        'ceo_review_status',
        'ceo_review_status_at',
        'ceo_review_status_by',
        'employment_status',
        'employment_status_at',
        'employment_status_by',
        'public_profile_enabled',
        'public_profile_token',
        'public_profile_expires_at',
        'public_profile_expiry_date',
        'employee_id_photo',
        'is_active',
        'created_by',
        'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'first_name' => 'required|min_length[2]|max_length[50]',
        'last_name' => 'required|min_length[2]|max_length[50]',
        'email_address' => 'permit_empty|valid_email|max_length[100]|is_unique[employee_personal_information.email_address,id,{id}]',
        'agency_id' => 'required|integer',
        'gender' => 'required|in_list[male,female]',
        'employment_number' => 'permit_empty|max_length[20]|is_unique[employee_personal_information.employment_number,id,{id}]',
        'nid_number' => 'permit_empty|max_length[20]|is_unique[employee_personal_information.nid_number,id,{id}]',
        'middle_name' => 'permit_empty|max_length[50]',
        'date_of_birth' => 'permit_empty|valid_date',
        'date_of_commencement' => 'permit_empty|valid_date',
        'marital_status' => 'permit_empty|in_list[single,married,divorced,widowed]',
        'spouse_name' => 'permit_empty|max_length[100]',
        'number_of_children' => 'permit_empty|integer|greater_than_equal_to[0]',
        'home_province' => 'permit_empty|max_length[50]',
        'home_district' => 'permit_empty|max_length[50]',
        'home_village' => 'permit_empty|max_length[100]',
        'mobile_number' => 'permit_empty|max_length[20]',
        'emergency_contact_person' => 'permit_empty|max_length[100]',
        'emergency_contact_phone' => 'permit_empty|max_length[20]',
        'designation' => 'permit_empty|max_length[100]',
        'department' => 'permit_empty|max_length[50]',
        'bank_name' => 'permit_empty|max_length[100]',
        'bank_branch' => 'permit_empty|max_length[100]',
        'account_name' => 'permit_empty|max_length[255]',
        'account_number' => 'permit_empty|max_length[50]',
        'account_type' => 'permit_empty|max_length[50]',
        'nasfund_member_name' => 'permit_empty|max_length[255]',
        'nasfund_membership_number' => 'permit_empty|max_length[50]',
        'nasfund_year_joined' => 'permit_empty|max_length[10]',
        'nasfund_branch' => 'permit_empty|max_length[100]',
        'onboard_status' => 'permit_empty|in_list[pending,in_progress,completed,rejected]',
        'hr_review_status' => 'permit_empty|in_list[pending,approved,rejected]',
        'supervisor_review_status' => 'permit_empty|in_list[pending,approved,rejected]',
        'ceo_review_status' => 'permit_empty|in_list[pending,approved,rejected]',
        'employment_status' => 'permit_empty|in_list[pending,active,inactive,terminated]',
        'public_profile_enabled' => 'permit_empty|in_list[0,1]',
        'public_profile_token' => 'permit_empty|max_length[255]',
        'public_profile_expiry_date' => 'permit_empty|valid_date',
        'employee_id_photo' => 'permit_empty|max_length[255]',
        'is_active' => 'permit_empty|in_list[0,1]'
    ];

    protected $validationMessages = [
        'email_address' => [
            'valid_email' => 'Please provide a valid email address format.',
            'is_unique' => 'This email address is already registered.'
        ],
        'employment_number' => [
            'is_unique' => 'This employment number is already in use.'
        ],
        'nid_number' => [
            'is_unique' => 'This NID number is already registered.'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get employees by agency with pagination
     */
    public function getEmployeesByAgency(int $agencyId, int $perPage = 10, string $search = '')
    {
        $this->where('agency_id', $agencyId);
        
        if (!empty($search)) {
            $this->groupStart()
                 ->like('first_name', $search)
                 ->orLike('last_name', $search)
                 ->orLike('email_address', $search)
                 ->orLike('employment_number', $search)
                 ->groupEnd();
        }
        
        return $this->orderBy('created_at', 'DESC')->paginate($perPage);
    }

    /**
     * Get employee by ID and agency
     */
    public function getEmployeeByAgency(int $employeeId, int $agencyId): ?array
    {
        return $this->where(['id' => $employeeId, 'agency_id' => $agencyId])->first();
    }

    /**
     * Check if employee belongs to agency
     */
    public function belongsToAgency(int $employeeId, int $agencyId): bool
    {
        return $this->where(['id' => $employeeId, 'agency_id' => $agencyId])->countAllResults() > 0;
    }

    /**
     * Generate unique employment number
     */
    public function generateUniqueEmployeeNumber(int $agencyId): string
    {
        $agencyModel = new AgencyModel();
        $agency = $agencyModel->find($agencyId);
        $agencyCode = $agency['agency_code'] ?? 'EMP';
        
        $year = date('Y');
        $prefix = strtoupper($agencyCode) . $year;
        
        // Get the last employee number for this agency and year
        $lastEmployee = $this->where('agency_id', $agencyId)
                            ->where('employment_number LIKE', $prefix . '%')
                            ->orderBy('employment_number', 'DESC')
                            ->first();
        
        if ($lastEmployee && !empty($lastEmployee['employment_number'])) {
            $lastNumber = (int) substr($lastEmployee['employment_number'], strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate public profile token
     */
    public function generatePublicProfileToken(): string
    {
        do {
            $token = bin2hex(random_bytes(32));
        } while ($this->where('public_profile_token', $token)->first());
        
        return $token;
    }

    /**
     * Get employees by onboard status
     */
    public function getEmployeesByOnboardStatus(int $agencyId, string $status): array
    {
        return $this->where(['agency_id' => $agencyId, 'onboard_status' => $status])
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Update onboard status
     */
    public function updateOnboardStatus(int $employeeId, string $status, int $userId, string $remarks = null): bool
    {
        $data = [
            'onboard_status' => $status,
            'onboard_status_at' => date('Y-m-d H:i:s'),
            'onboard_status_by' => $userId
        ];
        
        if ($remarks) {
            $data['onboard_status_remarks'] = $remarks;
        }
        
        return $this->update($employeeId, $data);
    }

    /**
     * Get dashboard statistics for agency
     */
    public function getDashboardStats(int $agencyId): array
    {
        $total = $this->where('agency_id', $agencyId)->countAllResults();
        $pending = $this->where(['agency_id' => $agencyId, 'onboard_status' => 'pending'])->countAllResults();
        $inProgress = $this->where(['agency_id' => $agencyId, 'onboard_status' => 'in_progress'])->countAllResults();
        $completed = $this->where(['agency_id' => $agencyId, 'onboard_status' => 'completed'])->countAllResults();
        $active = $this->where(['agency_id' => $agencyId, 'employment_status' => 'active'])->countAllResults();
        
        return [
            'total_employees' => $total,
            'pending_onboarding' => $pending,
            'in_progress_onboarding' => $inProgress,
            'completed_onboarding' => $completed,
            'active_employees' => $active
        ];
    }
}
