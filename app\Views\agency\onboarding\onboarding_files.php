<?= $this->extend('templates/agency_portal_template') ?>

<?= $this->section('content') ?>

<style>
/* Mobile-friendly action buttons */
@media (max-width: 767.98px) {
    .action-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.25rem;
        min-width: 45px;
    }
    .action-btn i {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }
    .action-btn span {
        font-size: 0.65rem;
        line-height: 1;
    }
}

@media (min-width: 768px) {
    .action-btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.5rem;
    }
    .action-btn i {
        font-size: 1rem;
    }
    .action-btn span {
        font-size: 0.75rem;
    }
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.file-size {
    font-size: 0.8rem;
    color: #6c757d;
}
</style>

<!-- Navigation Buttons -->
<div class="row mb-4">
    <div class="col-6">
        <a href="<?= base_url('agency/onboarding') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Employee List
        </a>
    </div>
    <div class="col-6 text-end">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/assessment') ?>" class="btn btn-success">
            <i class="bi bi-clipboard-check"></i> Assessment
        </a>
    </div>
</div>

<!-- Onboarding Steps Navigation -->
<div class="row mb-4">
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/profile') ?>" class="btn btn-outline-primary w-100">
            <i class="bi bi-person"></i><br>
            <small>Personal Info</small>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/education') ?>" class="btn btn-outline-info w-100">
            <i class="bi bi-mortarboard"></i><br>
            <small>Education</small>
            <span class="badge bg-secondary ms-1"><?= $education_stats['total_qualifications'] ?></span>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/memberships') ?>" class="btn btn-outline-success w-100">
            <i class="bi bi-award"></i><br>
            <small>Memberships</small>
            <span class="badge bg-secondary ms-1"><?= $membership_stats['total_memberships'] ?></span>
        </a>
    </div>
    <div class="col-md-2 mb-2">
        <button class="btn btn-warning w-100" disabled>
            <i class="bi bi-file-earmark"></i><br>
            <small>Files</small>
            <span class="badge bg-secondary ms-1"><?= $stats['total_files'] ?></span>
        </button>
    </div>
    <div class="col-md-3 mb-2">
        <a href="<?= base_url('agency/onboarding/' . $employee['id'] . '/employment-history') ?>" class="btn btn-outline-warning w-100">
            <i class="bi bi-briefcase"></i><br>
            <small>Employment</small>
            <span class="badge bg-secondary ms-1"><?= $employment_stats['total_employments'] ?></span>
        </a>
    </div>
</div>

<!-- Page Header -->
<div class="text-center mb-4">
    <h2 class="mb-1">
        <i class="bi bi-person-circle text-primary"></i>
        <?= esc($employee['first_name'] . ' ' . $employee['last_name']) ?>
    </h2>
    <p class="text-muted mb-0">
        <?php if (!empty($employee['employment_number'])): ?>
            File Number: <?= esc($employee['employment_number']) ?> •
        <?php endif; ?>
        Personal Files & Documents
    </p>
</div>

<!-- Files Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="bi bi-file-earmark text-warning" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1"><?= $stats['total_files'] ?></h4>
                <p class="text-muted mb-0">Total Files</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <i class="bi bi-file-earmark-check text-success" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1"><?= $stats['verified_files'] ?></h4>
                <p class="text-muted mb-0">Verified</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="bi bi-file-earmark-x text-danger" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1"><?= $stats['unverified_files'] ?></h4>
                <p class="text-muted mb-0">Unverified</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="bi bi-cloud-upload text-info" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1">PDF|JPG|PNG</h4>
                <p class="text-muted mb-0">20MB Max</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Files Content -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-file-earmark text-warning"></i>
            Personal Files & Documents
        </h5>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFileModal">
                <i class="bi bi-plus-circle"></i> Add File
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($file_records)): ?>
            <div class="text-center py-5">
                <i class="bi bi-file-earmark text-muted" style="font-size: 4rem;"></i>
                <h5 class="text-muted mt-3">No files found</h5>
                <p class="text-muted">Start by uploading the employee's personal files and documents.</p>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFileModal">
                    <i class="bi bi-plus-circle"></i> Upload First File
                </button>
            </div>
        <?php else: ?>
            <!-- Files Table -->
            <div class="table-responsive">
                <table class="table table-hover align-middle">
                    <thead class="table-light">
                        <tr>
                            <th scope="col" width="30%">
                                <i class="bi bi-file-earmark text-primary"></i> File Title
                            </th>
                            <th scope="col" width="35%">
                                <i class="bi bi-card-text text-info"></i> Description
                            </th>
                            <th scope="col" width="10%">
                                <i class="bi bi-check-circle text-success"></i> Status
                            </th>
                            <th scope="col" width="10%">
                                <i class="bi bi-calendar-event text-warning"></i> Uploaded
                            </th>
                            <th scope="col" width="15%" class="text-center">
                                <i class="bi bi-gear text-secondary"></i> Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($file_records as $file): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <?php
                                        $extension = pathinfo($file['file_path'], PATHINFO_EXTENSION);
                                        $iconClass = match(strtolower($extension)) {
                                            'pdf' => 'bi-file-earmark-pdf text-danger',
                                            'doc', 'docx' => 'bi-file-earmark-word text-primary',
                                            'jpg', 'jpeg', 'png' => 'bi-file-earmark-image text-success',
                                            default => 'bi-file-earmark text-secondary'
                                        };
                                        ?>
                                        <i class="<?= $iconClass ?> file-icon"></i>
                                        <div>
                                            <strong><?= esc($file['file_title']) ?></strong>
                                            <div class="file-size"><?= strtoupper($extension) ?> File</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small><?= esc($file['file_description'] ?: 'No description provided') ?></small>
                                </td>
                                <td>
                                    <?php if ($file['is_verified']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle"></i> Verified
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">
                                            <i class="bi bi-clock"></i> Pending
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('M d, Y', strtotime($file['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="d-flex gap-1 justify-content-center flex-wrap">
                                        <button type="button" class="btn btn-outline-info action-btn" 
                                                onclick="viewFile('<?= base_url($file['file_path']) ?>', '<?= esc($file['file_title']) ?>', '<?= strtolower($extension) ?>')" 
                                                title="View File">
                                            <i class="bi bi-eye"></i>
                                            <span class="d-none d-sm-inline">View</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-primary action-btn"
                                                onclick="editFile(<?= $file['id'] ?>)" title="Edit File">
                                            <i class="bi bi-pencil"></i>
                                            <span class="d-none d-sm-inline">Edit</span>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger action-btn"
                                                onclick="deleteFile(<?= $file['id'] ?>)" title="Delete File">
                                            <i class="bi bi-trash"></i>
                                            <span class="d-none d-sm-inline">Delete</span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add File Modal -->
<div class="modal fade" id="addFileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload New File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addFileForm" method="POST" action="<?= base_url('agency/onboarding/' . $employee['id'] . '/files/store') ?>" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="file_title" class="form-label">File Title <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="file_title" name="file_title" required>
                                <div class="form-text">Enter a descriptive title for this file</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">File Type</label>
                                <div class="form-text">PDF, JPG, PNG only</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="file_description" class="form-label">Description</label>
                        <textarea class="form-control" id="file_description" name="file_description" rows="3" 
                                  placeholder="Optional description of the file contents"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="document" class="form-label">Select File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="document" name="document" 
                               accept=".pdf,.jpg,.jpeg,.png" required>
                        <div class="form-text">Maximum file size: 20MB. Allowed formats: PDF, JPG, PNG only</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-cloud-upload"></i> Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit File Modal -->
<div class="modal fade" id="editFileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editFileForm" method="POST" enctype="multipart/form-data">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <!-- Form fields will be populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">Update File</span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Updating...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete File Modal -->
<div class="modal fade" id="deleteFileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <h5 class="mt-3">Are you sure?</h5>
                    <p class="text-muted">This action cannot be undone. The file will be permanently deleted.</p>
                </div>
                <form id="deleteFileForm" method="POST">
                    <?= csrf_field() ?>
                    <div class="d-flex justify-content-center gap-2">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <span class="btn-text">Delete File</span>
                            <span class="btn-loading d-none">
                                <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                Deleting...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- File Viewer Modal -->
<div class="modal fade" id="fileViewerModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fileViewerTitle">PDF Viewer</h5>
                <div class="d-flex align-items-center gap-3">
                    <!-- PDF Controls -->
                    <div id="pdfControls" class="d-none">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="prevPage" title="Previous Pages">
                                <i class="bi bi-chevron-left"></i>
                            </button>
                            <span class="btn btn-outline-secondary disabled" id="pageInfo">Pages 1-5 of 0</span>
                            <button type="button" class="btn btn-outline-secondary" id="nextPage" title="Next Pages">
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                        <div class="btn-group ms-2" role="group">
                            <button type="button" class="btn btn-outline-secondary" id="zoomOut" title="Zoom Out">
                                <i class="bi bi-zoom-out"></i>
                            </button>
                            <span class="btn btn-outline-secondary disabled" id="zoomLevel">100%</span>
                            <button type="button" class="btn btn-outline-secondary" id="zoomIn" title="Zoom In">
                                <i class="bi bi-zoom-in"></i>
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>
            <div class="modal-body p-0">
                <div id="fileViewerContent" class="h-100 d-flex align-items-center justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<!-- PDF.js Library -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

<script>
// PDF.js worker configuration
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

// File records data for JavaScript
const fileRecords = <?= json_encode($file_records) ?>;
const baseUrl = '<?= base_url() ?>';
const employeeId = <?= $employee['id'] ?>;

// PDF viewer variables
let currentPdf = null;
let currentPageGroup = 1;
let totalPages = 0;
let currentScale = 1.0;
const minScale = 0.5;
const maxScale = 3.0;
const scaleStep = 0.25;
const pagesPerGroup = 5;

// Load and render PDF
function loadPDF(pdfUrl) {
    const content = document.getElementById('fileViewerContent');
    const controls = document.getElementById('pdfControls');
    
    // Show loading spinner
    content.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    `;
    
    pdfjsLib.getDocument(pdfUrl).promise.then(function(pdf) {
        currentPdf = pdf;
        totalPages = pdf.numPages;
        currentPageGroup = 1;
        currentScale = 1.0;
        
        // Show controls
        if (controls) {
            controls.classList.remove('d-none');
        }
        
        updatePageInfo();
        renderPageGroup();
        updateNavigationButtons();
    }).catch(function(error) {
        console.error('Error loading PDF:', error);
        content.innerHTML = `
            <div class="text-center p-5">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                <h5 class="text-danger mt-3">Error Loading PDF</h5>
                <p class="text-muted">Unable to load the PDF file. Please try downloading it directly.</p>
                <a href="${pdfUrl}" target="_blank" class="btn btn-primary">
                    <i class="bi bi-download"></i> Download PDF
                </a>
            </div>
        `;
    });
}

// Render current group of PDF pages
function renderPageGroup() {
    if (!currentPdf) return;
    
    const startPage = (currentPageGroup - 1) * pagesPerGroup + 1;
    const endPage = Math.min(startPage + pagesPerGroup - 1, totalPages);
    
    const content = document.getElementById('fileViewerContent');
    content.innerHTML = '<div class="pdf-container p-3" style="overflow-y: auto; height: 100%; text-align: center;"></div>';
    const container = content.querySelector('.pdf-container');
    
    const promises = [];
    for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
        promises.push(renderPage(pageNum, container));
    }
    
    Promise.all(promises).then(() => {
        console.log(`Rendered pages ${startPage}-${endPage}`);
    });
}

// Render individual PDF page
function renderPage(pageNum, container) {
    return currentPdf.getPage(pageNum).then(function(page) {
        const viewport = page.getViewport({ scale: currentScale });
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        canvas.style.marginBottom = '20px';
        canvas.style.display = 'block';
        canvas.style.marginLeft = 'auto';
        canvas.style.marginRight = 'auto';
        canvas.style.border = '1px solid #ddd';
        canvas.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        canvas.style.maxWidth = '100%';
        canvas.style.height = 'auto';
        
        // Add page number label
        const pageLabel = document.createElement('div');
        pageLabel.textContent = `Page ${pageNum}`;
        pageLabel.style.textAlign = 'center';
        pageLabel.style.marginBottom = '10px';
        pageLabel.style.fontWeight = 'bold';
        pageLabel.style.color = '#666';
        
        container.appendChild(pageLabel);
        container.appendChild(canvas);
        
        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };
        
        return page.render(renderContext).promise;
    }).catch(function(error) {
        console.error('Error rendering page:', error);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'text-center p-3';
        errorDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
            <p class="text-danger mt-3">Error rendering page ${pageNum}</p>
        `;
        container.appendChild(errorDiv);
    });
}

// Update page info display
function updatePageInfo() {
    const startPage = (currentPageGroup - 1) * pagesPerGroup + 1;
    const endPage = Math.min(startPage + pagesPerGroup - 1, totalPages);
    const pageInfoElement = document.getElementById('pageInfo');
    const zoomLevelElement = document.getElementById('zoomLevel');
    
    if (pageInfoElement) {
        if (totalPages <= pagesPerGroup) {
            pageInfoElement.textContent = `Pages 1-${totalPages} of ${totalPages}`;
        } else {
            pageInfoElement.textContent = `Pages ${startPage}-${endPage} of ${totalPages}`;
        }
    }
    if (zoomLevelElement) {
        zoomLevelElement.textContent = `${Math.round(currentScale * 100)}%`;
    }
}

// Update navigation button states
function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    if (prevBtn) prevBtn.disabled = currentPageGroup <= 1;
    if (nextBtn) nextBtn.disabled = (currentPageGroup * pagesPerGroup) >= totalPages;
}

// View PDF file function
function viewPDF(filePath, fileName) {
    document.getElementById('fileViewerTitle').textContent = fileName || 'PDF Viewer';
    loadPDF(filePath);
    new bootstrap.Modal(document.getElementById('fileViewerModal')).show();
}

// Comprehensive file viewer function
function viewFile(filePath, fileName, fileType) {
    const content = document.getElementById('fileViewerContent');
    const controls = document.getElementById('pdfControls');
    
    document.getElementById('fileViewerTitle').textContent = fileName || 'File Viewer';
    
    if (fileType === 'pdf') {
        viewPDF(filePath, fileName);
        return;
    }
    
    // Hide PDF controls for non-PDF files
    if (controls) controls.classList.add('d-none');
    
    if (['jpg', 'jpeg', 'png', 'gif'].includes(fileType)) {
        // Show loading spinner
        content.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        `;
        
        const img = new Image();
        img.onload = function() {
            content.innerHTML = `
                <div class="text-center p-3 h-100 d-flex align-items-center justify-content-center">
                    <img src="${filePath}" alt="${fileName}" class="img-fluid" style="max-height: 90vh; max-width: 100%; object-fit: contain;">
                </div>
            `;
        };
        img.onerror = function() {
            content.innerHTML = `
                <div class="text-center p-5">
                    <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    <h5 class="text-danger mt-3">Error Loading Image</h5>
                    <p class="text-muted">Unable to load the image file. Please try downloading it directly.</p>
                    <a href="${filePath}" target="_blank" class="btn btn-primary">
                        <i class="bi bi-download"></i> Download Image
                    </a>
                </div>
            `;
        };
        img.src = filePath;
    } else {
        content.innerHTML = `
            <div class="text-center p-5">
                <i class="bi bi-file-earmark text-muted" style="font-size: 4rem;"></i>
                <h5 class="text-muted mt-3">File Preview Not Available</h5>
                <p class="text-muted">This file type cannot be previewed in the browser.</p>
                <a href="${filePath}" target="_blank" class="btn btn-primary">
                    <i class="bi bi-download"></i> Download File
                </a>
            </div>
        `;
    }
    
    new bootstrap.Modal(document.getElementById('fileViewerModal')).show();
}

// Setup PDF navigation event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Previous page group
    const prevBtn = document.getElementById('prevPage');
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            if (currentPageGroup > 1) {
                currentPageGroup--;
                renderPageGroup();
                updatePageInfo();
                updateNavigationButtons();
            }
        });
    }
    
    // Next page group
    const nextBtn = document.getElementById('nextPage');
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            if ((currentPageGroup * pagesPerGroup) < totalPages) {
                currentPageGroup++;
                renderPageGroup();
                updatePageInfo();
                updateNavigationButtons();
            }
        });
    }
    
    // Zoom in
    const zoomInBtn = document.getElementById('zoomIn');
    if (zoomInBtn) {
        zoomInBtn.addEventListener('click', function() {
            currentScale = Math.min(currentScale + scaleStep, maxScale);
            if (currentPdf) {
                renderPageGroup();
                updatePageInfo();
            }
        });
    }
    
    // Zoom out
    const zoomOutBtn = document.getElementById('zoomOut');
    if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', function() {
            currentScale = Math.max(currentScale - scaleStep, minScale);
            if (currentPdf) {
                renderPageGroup();
                updatePageInfo();
            }
        });
    }
    
    // Reset modal when closed
    const modal = document.getElementById('fileViewerModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            currentPdf = null;
            currentPageGroup = 1;
            totalPages = 0;
            currentScale = 1.0;
            
            const content = document.getElementById('fileViewerContent');
            if (content) {
                content.innerHTML = `
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                `;
            }
            
            const controls = document.getElementById('pdfControls');
            if (controls) {
                controls.classList.add('d-none');
            }
        });
    }
});

// ... rest of existing code ...

function editFile(fileId) {
    const file = fileRecords.find(f => f.id == fileId);
    if (!file) return;

    const form = document.getElementById('editFileForm');
    form.action = `${baseUrl}agency/onboarding/${employeeId}/files/${fileId}/update`;

    const modalBody = form.querySelector('.modal-body');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label for="edit_file_title" class="form-label">File Title <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="edit_file_title" name="file_title"
                           value="${file.file_title}" required>
                    <div class="form-text">Enter a descriptive title for this file</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">File Type</label>
                    <div class="form-text">PDF, JPG, PNG only</div>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label for="edit_file_description" class="form-label">Description</label>
            <textarea class="form-control" id="edit_file_description" name="file_description" rows="3"
                      placeholder="Optional description of the file contents">${file.file_description || ''}</textarea>
        </div>

        <div class="mb-3">
            <label for="edit_document" class="form-label">Replace File (Optional)</label>
            <input type="file" class="form-control" id="edit_document" name="document"
                   accept=".pdf,.jpg,.jpeg,.png">
            <div class="form-text">
                Maximum file size: 20MB. Allowed formats: PDF, JPG, PNG only<br>
                ${file.file_path ? 'Current file will be replaced if new file is uploaded.' : 'No file currently attached.'}
            </div>
        </div>
    `;

    new bootstrap.Modal(document.getElementById('editFileModal')).show();
}

function deleteFile(fileId) {
    const form = document.getElementById('deleteFileForm');
    form.action = `${baseUrl}agency/onboarding/${employeeId}/files/${fileId}/delete`;

    new bootstrap.Modal(document.getElementById('deleteFileModal')).show();
}

// Form submission handlers
document.getElementById('addFileForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    const btnText = submitBtn.querySelector('.btn-text') || submitBtn;
    const btnLoading = submitBtn.querySelector('.btn-loading');

    if (btnLoading) {
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
    }
    submitBtn.disabled = true;
});

document.getElementById('editFileForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    if (btnText && btnLoading) {
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
    }
    submitBtn.disabled = true;
});

document.getElementById('deleteFileForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');

    if (btnText && btnLoading) {
        btnText.classList.add('d-none');
        btnLoading.classList.remove('d-none');
    }
    submitBtn.disabled = true;
});
</script>
<?= $this->endSection() ?>
