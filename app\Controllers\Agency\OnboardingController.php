<?php

namespace App\Controllers\Agency;

use App\Models\EmployeePersonalInformationModel;
use App\Models\EmployeeEducationModel;
use App\Models\EmployeeProfessionalMembershipModel;
use App\Models\EmployeeEmploymentHistoryModel;
use App\Models\EmployeeFilesModel;
use App\Models\EducationQualificationsModel;

class OnboardingController extends AgencyBaseController
{
    protected $employeeModel;
    protected $educationModel;
    protected $membershipModel;
    protected $historyModel;
    protected $filesModel;
    protected $qualificationsModel;

    public function __construct()
    {
        parent::__construct();
        $this->employeeModel = new EmployeePersonalInformationModel();
        $this->educationModel = new EmployeeEducationModel();
        $this->membershipModel = new EmployeeProfessionalMembershipModel();
        $this->historyModel = new EmployeeEmploymentHistoryModel();
        $this->filesModel = new EmployeeFilesModel();
        $this->qualificationsModel = new EducationQualificationsModel();
    }

    /**
     * Display onboarding dashboard (GET)
     */
    public function index()
    {
        $this->setPageTitle('Employee Onboarding');
        $this->addBreadcrumb('Onboarding');

        $agencyId = $this->getCurrentAgencyId();
        
        // Get all employees for client-side DataTables
        $employees = $this->employeeModel->where('agency_id', $agencyId)
                                        ->orderBy('created_at', 'DESC')
                                        ->findAll();

        $data = [
            'employees' => $employees,
            'stats' => $this->employeeModel->getDashboardStats($agencyId)
        ];

        return $this->renderView('agency/onboarding/onboarding_index', $data);
    }

    /**
     * Show employee creation form (GET)
     */
    public function create()
    {
        $this->setPageTitle('Create New Employee');
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Create Employee');

        return $this->renderView('agency/onboarding/onboarding_create');
    }

    /**
     * Store new employee record (POST)
     */
    public function store()
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/create'));
        }

        $agencyId = $this->getCurrentAgencyId();

        $emailAddress = $this->request->getPost('email_address');

        $data = [
            'first_name' => $this->request->getPost('first_name'),
            'middle_name' => $this->request->getPost('middle_name'),
            'last_name' => $this->request->getPost('last_name'),
            'email_address' => !empty($emailAddress) ? $emailAddress : null,
            'gender' => $this->request->getPost('gender'),
            'agency_id' => $agencyId,
            'onboard_status' => 'pending',
            'public_profile_enabled' => $this->request->getPost('public_profile_enabled') ? 1 : 0,
            'created_by' => $this->auth->id()
        ];

        if ($this->employeeModel->save($data)) {
            $employeeId = $this->employeeModel->getInsertID();

            // Generate public profile token if enabled
            if ($data['public_profile_enabled']) {
                $token = $this->employeeModel->generatePublicProfileToken();
                $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));
                $this->employeeModel->update($employeeId, [
                    'public_profile_token' => $token,
                    'public_profile_expires_at' => $expiresAt
                ]);
            }

            $this->setFlashMessage('success', 'Employee record created successfully. Employment number will be assigned during profile completion.');
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
        } else {
            $this->setFlashMessage('error', 'Failed to create employee record. Please check the form and try again.');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show employee profile completion page (GET)
     */
    public function profile($employeeId)
    {
        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());
        
        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $this->setPageTitle('Employee Profile - ' . $employee['first_name'] . ' ' . $employee['last_name']);
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Employee Profile');

        $data = [
            'employee' => $employee,
            'education' => $this->educationModel->getEducationByEmployee($employeeId),
            'memberships' => $this->membershipModel->getMembershipsByEmployee($employeeId),
            'employment_history' => $this->historyModel->getHistoryByEmployee($employeeId),
            'education_stats' => $this->educationModel->getEducationStats($employeeId),
            'membership_stats' => $this->membershipModel->getMembershipStats($employeeId),
            'files_stats' => $this->filesModel->getFilesStats($employeeId),
            'employment_stats' => $this->historyModel->getEmploymentStats($employeeId)
        ];

        return $this->renderView('agency/onboarding/onboarding_profile', $data);
    }

    /**
     * Update employee profile information (POST)
     */
    public function updateProfile($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());
        
        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $data = [
            'agency_id' => $employee['agency_id'], // Preserve existing agency_id
            'first_name' => $this->request->getPost('first_name'),
            'middle_name' => $this->request->getPost('middle_name'),
            'last_name' => $this->request->getPost('last_name'),
            'email_address' => $this->request->getPost('email_address'),
            'gender' => $this->request->getPost('gender'),
            'nid_number' => $this->request->getPost('nid_number'),
            'date_of_birth' => $this->request->getPost('date_of_birth'),
            'date_of_commencement' => $this->request->getPost('date_of_commencement'),
            'marital_status' => $this->request->getPost('marital_status'),
            'spouse_name' => $this->request->getPost('spouse_name'),
            'number_of_children' => $this->request->getPost('number_of_children') ?: 0,
            'home_province' => $this->request->getPost('home_province'),
            'home_district' => $this->request->getPost('home_district'),
            'home_village' => $this->request->getPost('home_village'),
            'mobile_number' => $this->request->getPost('mobile_number'),
            'emergency_contact_person' => $this->request->getPost('emergency_contact_person'),
            'emergency_contact_phone' => $this->request->getPost('emergency_contact_phone'),
            'employment_number' => $this->request->getPost('employment_number'),
            'designation' => $this->request->getPost('designation'),
            'department' => $this->request->getPost('department'),
            'bank_name' => $this->request->getPost('bank_name'),
            'bank_branch' => $this->request->getPost('bank_branch'),
            'account_name' => $this->request->getPost('account_name'),
            'account_number' => $this->request->getPost('account_number'),
            'account_type' => $this->request->getPost('account_type'),
            'nasfund_member_name' => $this->request->getPost('nasfund_member_name'),
            'nasfund_membership_number' => $this->request->getPost('nasfund_membership_number'),
            'nasfund_year_joined' => $this->request->getPost('nasfund_year_joined'),
            'nasfund_branch' => $this->request->getPost('nasfund_branch'),
            'updated_by' => $this->auth->id()
        ];

        // Handle employee ID photo upload
        $photoFile = $this->request->getFile('employee_id_photo');
        if ($photoFile && $photoFile->isValid() && !$photoFile->hasMoved()) {
            // Validate file type
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
            if (in_array(strtolower($photoFile->getExtension()), $allowedTypes)) {
                // Validate file size (max 5MB)
                if ($photoFile->getSize() <= 5 * 1024 * 1024) {
                    // Create upload directory if it doesn't exist
                    $uploadPath = ROOTPATH . 'public/uploads/employee_photos/';
                    if (!is_dir($uploadPath)) {
                        mkdir($uploadPath, 0755, true);
                    }

                    // Delete old photo if exists
                    if (!empty($employee['employee_id_photo']) && file_exists(ROOTPATH . 'public/' . $employee['employee_id_photo'])) {
                        unlink(ROOTPATH . 'public/' . $employee['employee_id_photo']);
                    }

                    // Generate unique filename
                    $newName = 'emp_' . $employeeId . '_' . time() . '.' . $photoFile->getExtension();
                    
                    if ($photoFile->move($uploadPath, $newName)) {
                        $data['employee_id_photo'] = 'uploads/employee_photos/' . $newName;
                    }
                } else {
                    $this->setFlashMessage('warning', 'Employee ID photo file size must be less than 5MB.');
                }
            } else {
                $this->setFlashMessage('warning', 'Employee ID photo must be a valid image file (JPG, PNG, GIF).');
            }
        }

        // Set validation rules for update with correct ID
        $this->employeeModel->setValidationRules([
            'first_name' => 'required|min_length[2]|max_length[50]',
            'last_name' => 'required|min_length[2]|max_length[50]',
            'email_address' => 'permit_empty|valid_email|max_length[100]|is_unique[employee_personal_information.email_address,id,' . $employeeId . ']',
            'agency_id' => 'required|integer',
            'gender' => 'required|in_list[male,female]',
            'employment_number' => 'permit_empty|max_length[20]|is_unique[employee_personal_information.employment_number,id,' . $employeeId . ']',
            'nid_number' => 'permit_empty|max_length[20]|is_unique[employee_personal_information.nid_number,id,' . $employeeId . ']',
            'middle_name' => 'permit_empty|max_length[50]',
            'date_of_birth' => 'permit_empty|valid_date',
            'date_of_commencement' => 'permit_empty|valid_date',
            'marital_status' => 'permit_empty|in_list[single,married,divorced,widowed]',
            'spouse_name' => 'permit_empty|max_length[100]',
            'number_of_children' => 'permit_empty|integer|greater_than_equal_to[0]',
            'home_province' => 'permit_empty|max_length[50]',
            'home_district' => 'permit_empty|max_length[50]',
            'home_village' => 'permit_empty|max_length[100]',
            'mobile_number' => 'permit_empty|max_length[20]',
            'emergency_contact_person' => 'permit_empty|max_length[100]',
            'emergency_contact_phone' => 'permit_empty|max_length[20]',
            'designation' => 'permit_empty|max_length[100]',
            'department' => 'permit_empty|max_length[50]',
            'bank_name' => 'permit_empty|max_length[100]',
            'bank_branch' => 'permit_empty|max_length[100]',
            'account_name' => 'permit_empty|max_length[255]',
            'account_number' => 'permit_empty|max_length[50]',
            'account_type' => 'permit_empty|max_length[50]',
            'nasfund_member_name' => 'permit_empty|max_length[255]',
            'nasfund_membership_number' => 'permit_empty|max_length[50]',
            'nasfund_year_joined' => 'permit_empty|max_length[10]',
            'nasfund_branch' => 'permit_empty|max_length[100]',
            'public_profile_enabled' => 'permit_empty|in_list[0,1]',
            'public_profile_token' => 'permit_empty|max_length[255]',
            'public_profile_expiry_date' => 'permit_empty|valid_date',
            'employee_id_photo' => 'permit_empty|max_length[255]',
            'is_active' => 'permit_empty|in_list[0,1]'
        ]);

        if ($this->employeeModel->update($employeeId, $data)) {
            $this->setFlashMessage('success', 'Employee profile updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update employee profile. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
    }

    /**
     * Update public profile access settings (POST)
     */
    public function updatePublicAccess($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());
        
        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $publicEnabled = $this->request->getPost('public_profile_enabled') ? 1 : 0;
        $expiryDate = $this->request->getPost('public_profile_expiry_date');

        $data = [
            'public_profile_enabled' => $publicEnabled,
            'public_profile_expiry_date' => $expiryDate,
            'updated_by' => $this->auth->id()
        ];

        // If enabling public access and no token exists, generate one
        if ($publicEnabled && empty($employee['public_profile_token'])) {
            $data['public_profile_token'] = $this->employeeModel->generatePublicProfileToken();
            $data['public_profile_expires_at'] = $expiryDate ? date('Y-m-d H:i:s', strtotime($expiryDate . ' +1 day')) : date('Y-m-d H:i:s', strtotime('+30 days'));
        }

        // If disabling public access, clear token
        if (!$publicEnabled) {
            $data['public_profile_token'] = null;
            $data['public_profile_expires_at'] = null;
        }

        if ($this->employeeModel->update($employeeId, $data)) {
            $this->setFlashMessage('success', 'Public profile access settings updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update public profile access settings.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
    }

    /**
     * Show education management page (GET)
     */
    public function education($employeeId)
    {
        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());
        
        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $this->setPageTitle('Education - ' . $employee['first_name'] . ' ' . $employee['last_name']);
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Employee Profile', base_url('agency/onboarding/' . $employeeId . '/profile'));
        $this->addBreadcrumb('Education');

        $data = [
            'employee' => $employee,
            'education' => $this->educationModel->getEducationByEmployee($employeeId),
            'education_records' => $this->educationModel->getEducationByEmployee($employeeId),
            'basic_qualification' => $this->educationModel->getBasicQualification($employeeId),
            'additional_qualifications' => $this->educationModel->getAdditionalQualifications($employeeId),
            'stats' => $this->educationModel->getEducationStats($employeeId),
            'qualifications' => $this->qualificationsModel->getQualificationsGroupedForDropdown()
        ];

        return $this->renderView('agency/onboarding/onboarding_education', $data);
    }

    /**
     * Store education record (POST)
     */
    public function storeEducation($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());
        
        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        // Handle qualification name - use custom if "other" is selected
        $qualificationName = $this->request->getPost('qualification_name');
        $customQualificationName = $this->request->getPost('custom_qualification_name');
        $qualificationId = $this->request->getPost('qualification_id');

        if ($qualificationName === 'other' && !empty($customQualificationName)) {
            $qualificationName = $customQualificationName;
            $qualificationId = null; // No reference to predefined qualification
        }

        $data = [
            'employee_id' => $employeeId,
            'qualification_id' => $qualificationId ?: null,
            'qualification_type' => $this->request->getPost('qualification_type'),
            'qualification_name' => $qualificationName,
            'course_taken' => $this->request->getPost('course_taken'),
            'units' => $this->request->getPost('units'),
            'institution' => $this->request->getPost('institution'),
            'course_duration' => $this->request->getPost('course_duration'),
            'completion_year' => $this->request->getPost('completion_year'),
            'certificate_number' => $this->request->getPost('certificate_number')
        ];

        // Handle file upload if present
        $file = $this->request->getFile('document');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/education/', $newName);
            $data['document_path'] = 'public/uploads/education/' . $newName;
        }

        if ($this->educationModel->save($data)) {
            $this->setFlashMessage('success', 'Education record added successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to add education record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
    }

    /**
     * Update education record (POST)
     */
    public function updateEducation($employeeId, $educationId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $education = $this->educationModel->where(['id' => $educationId, 'employee_id' => $employeeId])->first();

        if (!$education) {
            $this->setFlashMessage('error', 'Education record not found.');
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
        }

        $data = [
            'qualification_type' => $this->request->getPost('qualification_type'),
            'qualification_name' => $this->request->getPost('qualification_name'),
            'course_taken' => $this->request->getPost('course_taken'),
            'units' => $this->request->getPost('units'),
            'institution' => $this->request->getPost('institution'),
            'course_duration' => $this->request->getPost('course_duration'),
            'completion_year' => $this->request->getPost('completion_year'),
            'certificate_number' => $this->request->getPost('certificate_number')
        ];

        // Handle file upload if present
        $file = $this->request->getFile('document');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Delete old file if exists
            if (!empty($education['document_path']) && file_exists(ROOTPATH . $education['document_path'])) {
                unlink(ROOTPATH . $education['document_path']);
            }

            $newName = $file->getRandomName();
            $file->move(ROOTPATH . 'public/uploads/education/', $newName);
            $data['document_path'] = 'public/uploads/education/' . $newName;
        }

        if ($this->educationModel->update($educationId, $data)) {
            $this->setFlashMessage('success', 'Education record updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update education record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
    }

    /**
     * Delete education record (POST)
     */
    public function deleteEducation($employeeId, $educationId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        if ($this->educationModel->deleteEducationRecord($educationId)) {
            $this->setFlashMessage('success', 'Education record deleted successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to delete education record.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/education'));
    }

    /**
     * Show professional memberships page (GET)
     */
    public function memberships($employeeId)
    {
        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $this->setPageTitle('Professional Memberships - ' . $employee['first_name'] . ' ' . $employee['last_name']);
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Employee Profile', base_url('agency/onboarding/' . $employeeId . '/profile'));
        $this->addBreadcrumb('Professional Memberships');

        $data = [
            'employee' => $employee,
            'membership_records' => $this->membershipModel->getMembershipsByEmployee($employeeId),
            'active_memberships' => $this->membershipModel->getActiveMemberships($employeeId),
            'expired_memberships' => $this->membershipModel->getExpiredMemberships($employeeId),
            'expiring_soon' => $this->membershipModel->getMembershipsExpiringSoon($employeeId),
            'stats' => $this->membershipModel->getMembershipStats($employeeId),
            'education_stats' => $this->educationModel->getEducationStats($employeeId),
            'employment_stats' => $this->historyModel->getEmploymentStats($employeeId)
        ];

        return $this->renderView('agency/onboarding/onboarding_memberships', $data);
    }

    /**
     * Store professional membership record (POST)
     */
    public function storeMembership($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        $rules = [
            'professional_affiliation' => 'required|max_length[255]',
            'current_status' => 'required|in_list[active,expired,suspended,pending]',
            'license_number' => 'permit_empty|max_length[100]',
            'renewal_date' => 'permit_empty|valid_date'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $data = [
            'employee_id' => $employeeId,
            'professional_affiliation' => $this->request->getPost('professional_affiliation'),
            'license_number' => $this->request->getPost('license_number'),
            'current_status' => $this->request->getPost('current_status'),
            'renewal_date' => $this->request->getPost('renewal_date')
        ];

        // Handle file upload if present
        $file = $this->request->getFile('document');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $allowedTypes = ['pdf', 'jpg', 'jpeg', 'png'];
            if (!in_array($file->getExtension(), $allowedTypes)) {
                return $this->response->setJSON([
                    'success' => false, 
                    'message' => 'Invalid file type. Allowed: PDF, JPG, PNG only'
                ]);
            }

            if ($file->getSize() > 20 * 1024 * 1024) { // 20MB limit
                return $this->response->setJSON([
                    'success' => false, 
                    'message' => 'File size exceeds 20MB limit'
                ]);
            }

            // Ensure upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/memberships/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $newName = $file->getRandomName();
            $file->move($uploadPath, $newName);
            $data['document_path'] = 'uploads/memberships/' . $newName;
        }

        if ($this->membershipModel->save($data)) {
            // If AJAX request, return JSON response
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true, 
                    'message' => 'Professional membership record added successfully',
                    'data' => $this->membershipModel->find($this->membershipModel->insertID())
                ]);
            }
            
            $this->setFlashMessage('success', 'Professional membership record added successfully.');
        } else {
            // If AJAX request, return JSON response
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false, 
                    'message' => 'Failed to add membership record',
                    'errors' => $this->membershipModel->errors()
                ]);
            }
            
            $this->setFlashMessage('error', 'Failed to add membership record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/memberships'));
    }

    /**
     * Update professional membership record (POST)
     */
    public function updateMembership($employeeId, $membershipId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        $membership = $this->membershipModel->where(['id' => $membershipId, 'employee_id' => $employeeId])->first();

        if (!$membership) {
            return $this->response->setJSON(['success' => false, 'message' => 'Membership record not found']);
        }

        $rules = [
            'professional_affiliation' => 'required|max_length[255]',
            'current_status' => 'required|in_list[active,expired,suspended,pending]',
            'license_number' => 'permit_empty|max_length[100]',
            'renewal_date' => 'permit_empty|valid_date'
        ];

        if (!$this->validate($rules)) {
            return $this->response->setJSON([
                'success' => false, 
                'message' => 'Validation failed',
                'errors' => $this->validator->getErrors()
            ]);
        }

        $data = [
            'professional_affiliation' => $this->request->getPost('professional_affiliation'),
            'license_number' => $this->request->getPost('license_number'),
            'current_status' => $this->request->getPost('current_status'),
            'renewal_date' => $this->request->getPost('renewal_date')
        ];

        // Handle file upload if present
        $file = $this->request->getFile('document');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $allowedTypes = ['pdf', 'jpg', 'jpeg', 'png'];
            if (!in_array($file->getExtension(), $allowedTypes)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid file type. Allowed: PDF, JPG, PNG only'
                ]);
            }

            if ($file->getSize() > 20 * 1024 * 1024) { // 20MB limit
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'File size exceeds 20MB limit'
                ]);
            }

            // Delete old file if exists
            if (!empty($membership['document_path']) && file_exists(ROOTPATH . 'public/' . $membership['document_path'])) {
                unlink(ROOTPATH . 'public/' . $membership['document_path']);
            }

            $uploadPath = ROOTPATH . 'public/uploads/memberships/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $newName = $file->getRandomName();
            $file->move($uploadPath, $newName);
            $data['document_path'] = 'uploads/memberships/' . $newName;
        }

        if ($this->membershipModel->update($membershipId, $data)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true, 
                    'message' => 'Professional membership record updated successfully',
                    'data' => $this->membershipModel->find($membershipId)
                ]);
            }
            
            $this->setFlashMessage('success', 'Professional membership record updated successfully.');
        } else {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false, 
                    'message' => 'Failed to update membership record',
                    'errors' => $this->membershipModel->errors()
                ]);
            }
            
            $this->setFlashMessage('error', 'Failed to update membership record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/memberships'));
    }

    /**
     * Delete professional membership record (POST)
     */
    public function deleteMembership($employeeId, $membershipId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        if ($this->membershipModel->deleteMembershipRecord($membershipId)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true, 
                    'message' => 'Professional membership record deleted successfully'
                ]);
            }
            
            $this->setFlashMessage('success', 'Professional membership record deleted successfully.');
        } else {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false, 
                    'message' => 'Failed to delete membership record'
                ]);
            }
            
            $this->setFlashMessage('error', 'Failed to delete membership record.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/memberships'));
    }

    /**
     * Get membership data as JSON (GET)
     */
    public function getMembership($employeeId, $membershipId)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/memberships'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        $membership = $this->membershipModel->getMembershipByEmployeeAndId($employeeId, $membershipId);

        if (!$membership) {
            return $this->response->setJSON(['success' => false, 'message' => 'Membership not found']);
        }

        return $this->response->setJSON(['success' => true, 'data' => $membership]);
    }

    /**
     * Show files management page (GET)
     */
    public function files($employeeId)
    {
        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $this->setPageTitle('Files - ' . $employee['first_name'] . ' ' . $employee['last_name']);
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Employee Profile', base_url('agency/onboarding/' . $employeeId . '/profile'));
        $this->addBreadcrumb('Files');

        $data = [
            'employee' => $employee,
            'file_records' => $this->filesModel->getFilesByEmployee($employeeId),
            'verified_files' => $this->filesModel->getVerifiedFiles($employeeId),
            'unverified_files' => $this->filesModel->getUnverifiedFiles($employeeId),
            'stats' => $this->filesModel->getFilesStats($employeeId),
            'education_stats' => $this->educationModel->getEducationStats($employeeId),
            'membership_stats' => $this->membershipModel->getMembershipStats($employeeId),
            'employment_stats' => $this->historyModel->getEmploymentStats($employeeId)
        ];

        return $this->renderView('agency/onboarding/onboarding_files', $data);
    }

    /**
     * Store file record (POST)
     */
    public function storeFile($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        $rules = [
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $this->validator->getErrors()
                ]);
            }

            $this->setFlashMessage('error', 'Please check the form and try again.');
            return redirect()->back()->withInput();
        }

        $data = [
            'employee_id' => $employeeId,
            'file_title' => $this->request->getPost('file_title'),
            'file_description' => $this->request->getPost('file_description'),
            'is_verified' => 0
        ];

        // Handle file upload if present
        $file = $this->request->getFile('document');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $allowedTypes = ['pdf', 'jpg', 'jpeg', 'png'];
            if (!in_array($file->getExtension(), $allowedTypes)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid file type. Allowed: PDF, JPG, PNG only'
                ]);
            }

            if ($file->getSize() > 20 * 1024 * 1024) { // 20MB limit
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'File size exceeds 20MB limit'
                ]);
            }

            // Ensure upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/files/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Generate unique filename
            $newName = $file->getRandomName();
            if ($file->move($uploadPath, $newName)) {
                $data['file_path'] = 'uploads/files/' . $newName;
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to upload file'
                ]);
            }
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please select a file to upload'
            ]);
        }

        if ($this->filesModel->save($data)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'data' => $this->filesModel->find($this->filesModel->getInsertID())
                ]);
            }

            $this->setFlashMessage('success', 'File uploaded successfully.');
        } else {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to save file record',
                    'errors' => $this->filesModel->errors()
                ]);
            }

            $this->setFlashMessage('error', 'Failed to save file record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/files'));
    }

    /**
     * Update file record (POST)
     */
    public function updateFile($employeeId, $fileId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        $fileRecord = $this->filesModel->where(['id' => $fileId, 'employee_id' => $employeeId])->first();

        if (!$fileRecord) {
            return $this->response->setJSON(['success' => false, 'message' => 'File record not found']);
        }

        $rules = [
            'file_title' => 'required|min_length[2]|max_length[255]',
            'file_description' => 'permit_empty'
        ];

        if (!$this->validate($rules)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $this->validator->getErrors()
                ]);
            }

            $this->setFlashMessage('error', 'Please check the form and try again.');
            return redirect()->back()->withInput();
        }

        $data = [
            'file_title' => $this->request->getPost('file_title'),
            'file_description' => $this->request->getPost('file_description')
        ];

        // Handle file upload if present
        $file = $this->request->getFile('document');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $allowedTypes = ['pdf', 'jpg', 'jpeg', 'png'];
            if (!in_array($file->getExtension(), $allowedTypes)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid file type. Allowed: PDF, JPG, PNG only'
                ]);
            }

            if ($file->getSize() > 20 * 1024 * 1024) { // 20MB limit
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'File size exceeds 20MB limit'
                ]);
            }

            // Delete old file if exists
            if (!empty($fileRecord['file_path']) && file_exists(ROOTPATH . 'public/' . $fileRecord['file_path'])) {
                unlink(ROOTPATH . 'public/' . $fileRecord['file_path']);
            }

            // Ensure upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/files/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            // Generate unique filename
            $newName = $file->getRandomName();
            if ($file->move($uploadPath, $newName)) {
                $data['file_path'] = 'uploads/files/' . $newName;
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to upload file'
                ]);
            }
        }

        if ($this->filesModel->update($fileId, $data)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File record updated successfully',
                    'data' => $this->filesModel->find($fileId)
                ]);
            }

            $this->setFlashMessage('success', 'File record updated successfully.');
        } else {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update file record',
                    'errors' => $this->filesModel->errors()
                ]);
            }

            $this->setFlashMessage('error', 'Failed to update file record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/files'));
    }

    /**
     * Delete file record (POST)
     */
    public function deleteFile($employeeId, $fileId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        if ($this->filesModel->deleteFileRecord($fileId)) {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File record deleted successfully'
                ]);
            }

            $this->setFlashMessage('success', 'File record deleted successfully.');
        } else {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to delete file record'
                ]);
            }

            $this->setFlashMessage('error', 'Failed to delete file record.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/files'));
    }

    /**
     * Verify file record (POST)
     */
    public function verifyFile($employeeId, $fileId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request method']);
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            return $this->response->setJSON(['success' => false, 'message' => 'Employee not found']);
        }

        $fileRecord = $this->filesModel->where(['id' => $fileId, 'employee_id' => $employeeId])->first();

        if (!$fileRecord) {
            return $this->response->setJSON(['success' => false, 'message' => 'File record not found']);
        }

        // Toggle verification status
        $newStatus = $fileRecord['is_verified'] ? 0 : 1;
        
        if ($this->filesModel->update($fileId, ['is_verified' => $newStatus])) {
            $statusText = $newStatus ? 'verified' : 'unverified';
            
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => "File {$statusText} successfully",
                    'is_verified' => $newStatus
                ]);
            }

            $this->setFlashMessage('success', "File {$statusText} successfully.");
        } else {
            if ($this->request->isAJAX()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update file verification status'
                ]);
            }

            $this->setFlashMessage('error', 'Failed to update file verification status.');
        }

        return redirect()->back();
    }

    /**
     * Show employment history page (GET)
     */
    public function employmentHistory($employeeId)
    {
        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $this->setPageTitle('Employment History - ' . $employee['first_name'] . ' ' . $employee['last_name']);
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Employee Profile', base_url('agency/onboarding/' . $employeeId . '/profile'));
        $this->addBreadcrumb('Employment History');

        $data = [
            'employee' => $employee,
            'employment_history' => $this->historyModel->getHistoryByEmployee($employeeId),
            'current_employment' => $this->historyModel->getCurrentEmployment($employeeId),
            'previous_employment' => $this->historyModel->getPreviousEmployment($employeeId),
            'stats' => $this->historyModel->getEmploymentStats($employeeId)
        ];

        return $this->renderView('agency/onboarding/onboarding_employment_history', $data);
    }

    /**
     * Store employment history record (POST)
     */
    public function storeEmploymentHistory($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $data = [
            'employee_id' => $employeeId,
            'employer_name' => $this->request->getPost('employer_name'),
            'designation' => $this->request->getPost('designation'),
            'start_date' => $this->request->getPost('start_date'),
            'end_date' => $this->request->getPost('end_date'),
            'is_current' => $this->request->getPost('is_current') ? 1 : 0
        ];

        // If this is marked as current employment, unset others
        if ($data['is_current']) {
            $this->historyModel->where('employee_id', $employeeId)->set(['is_current' => 0])->update();
            $data['end_date'] = null; // Current employment has no end date
        }

        if ($this->historyModel->save($data)) {
            $this->setFlashMessage('success', 'Employment history record added successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to add employment history record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
    }

    /**
     * Update employment history record (POST)
     */
    public function updateEmploymentHistory($employeeId, $historyId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $history = $this->historyModel->where(['id' => $historyId, 'employee_id' => $employeeId])->first();

        if (!$history) {
            $this->setFlashMessage('error', 'Employment history record not found.');
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
        }

        $data = [
            'employer_name' => $this->request->getPost('employer_name'),
            'designation' => $this->request->getPost('designation'),
            'start_date' => $this->request->getPost('start_date'),
            'end_date' => $this->request->getPost('end_date'),
            'is_current' => $this->request->getPost('is_current') ? 1 : 0
        ];

        // If this is marked as current employment, unset others
        if ($data['is_current']) {
            $this->historyModel->where('employee_id', $employeeId)
                              ->where('id !=', $historyId)
                              ->set(['is_current' => 0])
                              ->update();
            $data['end_date'] = null; // Current employment has no end date
        }

        if ($this->historyModel->update($historyId, $data)) {
            $this->setFlashMessage('success', 'Employment history record updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update employment history record. Please check the form and try again.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
    }

    /**
     * Delete employment history record (POST)
     */
    public function deleteEmploymentHistory($employeeId, $historyId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        if ($this->historyModel->delete($historyId)) {
            $this->setFlashMessage('success', 'Employment history record deleted successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to delete employment history record.');
        }

        return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/employment-history'));
    }

    /**
     * Generate public profile link (POST)
     */
    public function generatePublicLink($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $token = $this->employeeModel->generatePublicProfileToken();
        $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));

        $data = [
            'public_profile_token' => $token,
            'public_profile_expires_at' => $expiresAt,
            'public_profile_enabled' => 1
        ];

        if ($this->employeeModel->update($employeeId, $data)) {
            $publicUrl = base_url('public-profile/' . $token);
            $this->setFlashMessage('success', 'Public profile link generated successfully. Link: ' . $publicUrl);
        } else {
            $this->setFlashMessage('error', 'Failed to generate public profile link.');
        }

        return redirect()->to('/agency/onboarding/' . $employeeId . '/profile');
    }

    /**
     * Toggle public profile access (POST)
     */
    public function togglePublicAccess($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $enabled = $this->request->getPost('enabled') ? 1 : 0;

        if ($this->employeeModel->update($employeeId, ['public_profile_enabled' => $enabled])) {
            $status = $enabled ? 'enabled' : 'disabled';
            $this->setFlashMessage('success', 'Public profile access ' . $status . ' successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update public profile access.');
        }

        return redirect()->to('/agency/onboarding/' . $employeeId . '/profile');
    }

    /**
     * Update onboarding status (POST)
     */
    public function updateOnboardingStatus($employeeId)
    {
        if (!$this->request->getMethod() === 'POST') {
            return redirect()->to(base_url('agency/onboarding/' . $employeeId . '/profile'));
        }

        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());

        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $status = $this->request->getPost('status');
        $remarks = $this->request->getPost('remarks');
        $userId = $this->auth->id();

        if ($this->employeeModel->updateOnboardStatus($employeeId, $status, $userId, $remarks)) {
            $this->setFlashMessage('success', 'Onboarding status updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update onboarding status.');
        }

        return redirect()->to('/agency/onboarding/' . $employeeId . '/profile');
    }

    /**
     * Get employees data for DataTables (AJAX)
     */
    public function getEmployeesData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request']);
        }

        $agencyId = $this->getCurrentAgencyId();
        $draw = $this->request->getGet('draw');
        $start = $this->request->getGet('start') ?? 0;
        $length = $this->request->getGet('length') ?? 10;
        $searchValue = $this->request->getGet('search')['value'] ?? '';
        $orderColumn = $this->request->getGet('order')[0]['column'] ?? 0;
        $orderDir = $this->request->getGet('order')[0]['dir'] ?? 'desc';
        
        // Custom filters
        $statusFilter = $this->request->getGet('status_filter') ?? '';
        $employmentStatusFilter = $this->request->getGet('employment_status_filter') ?? '';

        // Define column mapping for ordering
        $columns = [
            0 => 'first_name',
            1 => 'employment_number',
            2 => 'email_address',
            3 => 'onboard_status',
            4 => 'employment_status',
            5 => 'created_at'
        ];

        $orderBy = $columns[$orderColumn] ?? 'created_at';

        // Get filtered data count
        $totalRecords = $this->employeeModel->getEmployeeCountByAgency($agencyId);
        $filteredRecords = $this->employeeModel->getEmployeeCountByAgency(
            $agencyId, 
            $searchValue, 
            $statusFilter, 
            $employmentStatusFilter
        );

        // Get actual data
        $employees = $this->employeeModel->getEmployeesForDataTable(
            $agencyId,
            $start,
            $length,
            $searchValue,
            $orderBy,
            $orderDir,
            $statusFilter,
            $employmentStatusFilter
        );

        $data = [];
        foreach ($employees as $employee) {
            $data[] = [
                'id' => $employee['id'],
                'employee' => [
                    'name' => trim($employee['first_name'] . ' ' . ($employee['middle_name'] ?? '') . ' ' . $employee['last_name']),
                    'first_name' => $employee['first_name'],
                    'middle_name' => $employee['middle_name'] ?? '',
                    'last_name' => $employee['last_name'],
                    'initials' => strtoupper(substr($employee['first_name'], 0, 1) . substr($employee['last_name'], 0, 1))
                ],
                'employment_number' => $employee['employment_number'] ?? 'Not assigned',
                'email_address' => $employee['email_address'] ?? '',
                'onboard_status' => [
                    'status' => $employee['onboard_status'],
                    'label' => ucfirst(str_replace('_', ' ', $employee['onboard_status'])),
                    'color' => $this->getStatusColor($employee['onboard_status'])
                ],
                'employment_status' => [
                    'status' => $employee['employment_status'],
                    'label' => ucfirst($employee['employment_status']),
                    'color' => $this->getEmploymentStatusColor($employee['employment_status'])
                ],
                'created_at' => date('M d, Y', strtotime($employee['created_at'])),
                'created_timestamp' => strtotime($employee['created_at']),
                'actions' => [
                    'profile_url' => base_url('agency/onboarding/' . $employee['id'] . '/profile'),
                    'education_url' => base_url('agency/onboarding/' . $employee['id'] . '/education'),
                    'memberships_url' => base_url('agency/onboarding/' . $employee['id'] . '/memberships'),
                    'history_url' => base_url('agency/onboarding/' . $employee['id'] . '/employment-history'),
                    'assessment_url' => base_url('agency/onboarding/' . $employee['id'] . '/assessment')
                ]
            ];
        }

        return $this->response->setJSON([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Get status color for badges
     */
    private function getStatusColor($status)
    {
        $colors = [
            'pending' => 'warning',
            'in_progress' => 'info',
            'completed' => 'success',
            'rejected' => 'danger'
        ];
        return $colors[$status] ?? 'secondary';
    }

    /**
     * Get employment status color for badges
     */
    private function getEmploymentStatusColor($status)
    {
        $colors = [
            'pending' => 'warning',
            'active' => 'success',
            'inactive' => 'secondary',
            'terminated' => 'danger'
        ];
        return $colors[$status] ?? 'secondary';
    }

    /**
     * Show comprehensive employee assessment view (GET)
     */
    public function assessment($employeeId)
    {
        $employee = $this->employeeModel->getEmployeeByAgency($employeeId, $this->getCurrentAgencyId());
        
        if (!$employee) {
            $this->setFlashMessage('error', 'Employee not found.');
            return redirect()->to(base_url('agency/onboarding'));
        }

        $this->setPageTitle('Assessment - ' . $employee['first_name'] . ' ' . $employee['last_name']);
        $this->addBreadcrumb('Onboarding', base_url('agency/onboarding'));
        $this->addBreadcrumb('Employee Assessment');

        // Gather all employee data for comprehensive assessment
        $data = [
            'employee' => $employee,
            'education_records' => $this->educationModel->getEducationByEmployee($employeeId),
            'membership_records' => $this->membershipModel->getMembershipsByEmployee($employeeId),
            'file_records' => $this->filesModel->getFilesByEmployee($employeeId),
            'employment_history' => $this->historyModel->getHistoryByEmployee($employeeId),
            'education_stats' => $this->educationModel->getEducationStats($employeeId),
            'membership_stats' => $this->membershipModel->getMembershipStats($employeeId),
            'files_stats' => $this->filesModel->getFilesStats($employeeId),
            'employment_stats' => $this->historyModel->getEmploymentStats($employeeId),
            'qualifications' => $this->qualificationsModel->getQualificationsGroupedForDropdown()
        ];

        return $this->renderView('agency/onboarding/onboarding_assessment', $data);
    }
}
