-- Fix MariaDB Connection Issues for <PERSON>eal<PERSON> Wokman
-- Run this script after connecting to <PERSON><PERSON><PERSON> as an admin user

-- Create a new user for the application
CREATE USER IF NOT EXISTS 'chealthwokman'@'localhost' IDENTIFIED BY 'chealthwokman123';
CREATE USER IF NOT EXISTS 'chealthwokman'@'127.0.0.1' IDENTIFIED BY 'chealthwokman123';
CREATE USER IF NOT EXISTS 'chealthwokman'@'%' IDENTIFIED BY 'chealthwokman123';

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS chealthwokman_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Grant all privileges on the database to the new user
GRANT ALL PRIVILEGES ON chealthwokman_db.* TO 'chealthwokman'@'localhost';
GRANT ALL PRIVILEGES ON chealthwokman_db.* TO 'chealthwokman'@'127.0.0.1';
GRANT ALL PRIVILEGES ON chealthwokman_db.* TO 'chealthwokman'@'%';

-- Also fix root user permissions if needed
UPDATE mysql.user SET host='%' WHERE user='root' AND host='localhost';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY '' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'127.0.0.1' IDENTIFIED BY '' WITH GRANT OPTION;

-- Refresh privileges
FLUSH PRIVILEGES;

-- Show users to verify
SELECT user, host FROM mysql.user WHERE user IN ('root', 'chealthwokman');
