<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddCourseTakenAndUnitsToEmployeeEducation extends Migration
{
    public function up()
    {
        // Add course_taken and units fields to employee_education table
        $fields = [
            'course_taken' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'qualification_name'
            ],
            'units' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'course_taken'
            ]
        ];
        
        $this->forge->addColumn('employee_education', $fields);
    }

    public function down()
    {
        // Drop the added columns
        $this->forge->dropColumn('employee_education', ['course_taken', 'units']);
    }
}
